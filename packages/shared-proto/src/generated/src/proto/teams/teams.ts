// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v3.20.3
// source: teams/teams.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.teams.v1";

/** Single rule definition */
export interface Rule {
  field: string;
  operator: string;
  value: string;
  precedence?: number | undefined;
}

/** Request to create a new routing rule */
export interface CreateRoutingRuleRequest {
  name: string;
  description?: string | undefined;
  teamId: string;
  evaluationOrder?: number | undefined;
  resultTeamId: string;
  andRules: Rule[];
  orRules: Rule[];
}

/** Request to update an existing routing rule */
export interface UpdateRoutingRuleRequest {
  id: string;
  name?: string | undefined;
  description?: string | undefined;
  evaluationOrder?: number | undefined;
  resultTeamId?: string | undefined;
  andRules: Rule[];
  orRules: Rule[];
}

/** Response for routing rule operations */
export interface CommonRoutingRuleResponse {
  id: string;
  name: string;
  description?: string | undefined;
  teamId: string;
  evaluationOrder: number;
  resultTeamId: string;
  fallbackTeamId?: string | undefined;
  andRules: Rule[];
  orRules: Rule[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface EmptyResponse {
}

export interface GetAllTeamsRequest {
}

export interface GetAllPublicTeamsRequest {
}

export interface GetTeamByIdRequest {
  id: string;
}

export interface AddTeamMemberRequest {
  teamId: string;
  userId?: string | undefined;
  email?: string | undefined;
  isAdmin?: boolean | undefined;
}

export interface RemoveTeamMemberRequest {
  teamId: string;
  memberId: string;
}

export interface TeamMemberResponse {
  id: string;
  name: string;
  email: string;
  invitedBy: string;
  teamId: string;
  teamName: string;
  isActive: boolean;
  role: string;
  isOwner: boolean;
  joinedAt: string;
}

export interface GetAllTeamMembersRequest {
  teamId: string;
}

export interface GetAllTeamMembersResponse {
  members: TeamMemberResponse[];
}

export interface CreateTeamRequest {
  name: string;
  description?: string | undefined;
  identifier?: string | undefined;
  parentTeamId?: string | undefined;
  isPrivate?: boolean | undefined;
  icon?: string | undefined;
}

export interface UpdateTeamRequest {
  id: string;
  name?: string | undefined;
  description?: string | undefined;
  isPrivate?: boolean | undefined;
  icon?: string | undefined;
}

export interface DeleteTeamRequest {
  id: string;
}

export interface CommonTeamResponse {
  id: string;
  name: string;
  parentTeamId?: string | undefined;
  parentTeamName?: string | undefined;
  teamId?: string | undefined;
  identifier?: string | undefined;
  description?: string | undefined;
  teamOwner?: string | undefined;
  teamOwnerId?: string | undefined;
  createdAt?: string | undefined;
  isActive?: boolean | undefined;
  isPrivate?: boolean | undefined;
  archivedAt?: string | undefined;
}

export interface GetAllTeamsResponse {
  teams: CommonTeamResponse[];
}

export interface GetAllPublicTeamsResponse {
  teams: CommonTeamResponse[];
}

/** Business time slot representing a start and end time */
export interface BusinessSlot {
  /** Start time in 24-hour format (HH:mm) */
  start: string;
  /** End time in 24-hour format (HH:mm) */
  end: string;
}

/** Business day configuration */
export interface BusinessDay {
  /** Whether the business day is active */
  isActive: boolean;
  /** The time slots for the business day */
  slots: BusinessSlot[];
}

/** Business days configuration */
export interface BusinessDays {
  /** The business hours for Monday */
  monday:
    | BusinessDay
    | undefined;
  /** The business hours for Tuesday */
  tuesday:
    | BusinessDay
    | undefined;
  /** The business hours for Wednesday */
  wednesday:
    | BusinessDay
    | undefined;
  /** The business hours for Thursday */
  thursday:
    | BusinessDay
    | undefined;
  /** The business hours for Friday */
  friday:
    | BusinessDay
    | undefined;
  /** The business hours for Saturday */
  saturday:
    | BusinessDay
    | undefined;
  /** The business hours for Sunday */
  sunday: BusinessDay | undefined;
}

/** Business days configuration */
export interface ResponseBusinessDays {
  /** The business hours for Monday */
  monday:
    | BusinessDay
    | undefined;
  /** The business hours for Tuesday */
  tuesday:
    | BusinessDay
    | undefined;
  /** The business hours for Wednesday */
  wednesday:
    | BusinessDay
    | undefined;
  /** The business hours for Thursday */
  thursday:
    | BusinessDay
    | undefined;
  /** The business hours for Friday */
  friday:
    | BusinessDay
    | undefined;
  /** The business hours for Saturday */
  saturday:
    | BusinessDay
    | undefined;
  /** The business hours for Sunday */
  sunday: BusinessDay | undefined;
}

export interface GetTeamConfigurationsRequest {
  teamId: string;
}

export interface TeamConfigurationsResponse {
  teamId: string;
  timezone: string;
  holidays: string[];
  routingRespectsTimezone: boolean;
  routingRespectsUserTimezone: boolean;
  routingRespectsUserAvailability: boolean;
  routingRespectsUserBusinessHours: boolean;
  userRoutingStrategy: string;
  dailyConfig: ResponseBusinessDays | undefined;
}

export interface UpdateTeamConfigurationsRequest {
  teamId: string;
  timezone?: string | undefined;
  routingRespectsTimezone?: boolean | undefined;
  routingRespectsUserTimezone?: boolean | undefined;
  routingRespectsUserAvailability?: boolean | undefined;
  routingRespectsUserBusinessHours?: boolean | undefined;
  userRoutingStrategy?: string | undefined;
  dailyConfig?: BusinessDays | undefined;
}

export interface CheckTeamAvailabilityRequest {
  teamId: string;
}

export interface CheckTeamAvailabilityResponse {
  isAvailable: boolean;
  reason: string;
}

/** Field metadata messages */
export interface GetTeamFieldMetadataRequest {
}

export interface GetTeamFieldMetadataResponse {
  fields: { [key: string]: TeamFieldMetadata };
}

export interface GetTeamFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TeamFieldMetadata | undefined;
}

export interface GetTeamMemberFieldMetadataRequest {
}

export interface GetTeamMemberFieldMetadataResponse {
  fields: { [key: string]: TeamFieldMetadata };
}

export interface GetTeamMemberFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TeamFieldMetadata | undefined;
}

export interface GetTeamConfigurationFieldMetadataRequest {
}

export interface GetTeamConfigurationFieldMetadataResponse {
  fields: { [key: string]: TeamFieldMetadata };
}

export interface GetTeamConfigurationFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TeamFieldMetadata | undefined;
}

export interface GetTeamCapacityFieldMetadataRequest {
}

export interface GetTeamCapacityFieldMetadataResponse {
  fields: { [key: string]: TeamFieldMetadata };
}

export interface GetTeamCapacityFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TeamFieldMetadata | undefined;
}

export interface GetBusinessHoursConfigFieldMetadataRequest {
}

export interface GetBusinessHoursConfigFieldMetadataResponse {
  fields: { [key: string]: TeamFieldMetadata };
}

export interface GetBusinessHoursConfigFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TeamFieldMetadata | undefined;
}

/** Entity data messages */
export interface GetTeamDataRequest {
  teamId: string;
  relations: string[];
}

export interface GetTeamDataResponse {
  data: string;
}

export interface GetTeamMemberDataRequest {
  teamId: string;
  memberId: string;
  relations: string[];
}

export interface GetTeamMemberDataResponse {
  data: string;
}

export interface GetTeamConfigurationDataRequest {
  teamId: string;
  relations: string[];
}

export interface GetTeamConfigurationDataResponse {
  data: string;
}

export interface GetTeamCapacityDataRequest {
  teamId: string;
  userId: string;
  relations: string[];
}

export interface GetTeamCapacityDataResponse {
  data: string;
}

export interface GetBusinessHoursConfigDataRequest {
  teamId: string;
  relations: string[];
}

export interface GetBusinessHoursConfigDataResponse {
  data: string;
}

/** Field metadata structure for teams */
export interface TeamFieldMetadata {
  /** Type of the field */
  type: string;
  /** Label for displaying */
  label: string;
  /** Description for displaying */
  description?:
    | string
    | undefined;
  /** Key of the field in findEntity api response */
  expression: string;
  /** If the field is a standard field */
  standard: boolean;
  /** If the field is enabled for SLA */
  slaEnabled?:
    | boolean
    | undefined;
  /** Constraints for the field */
  constraints?:
    | TeamFieldConstraints
    | undefined;
  /** Supported operators for the field */
  supportedOperators: TeamFieldOperator[];
}

/** Field constraints for teams */
export interface TeamFieldConstraints {
  /** For choice type */
  dynamicOptions?:
    | boolean
    | undefined;
  /** Fixed options for choice type */
  options: TeamFieldOption[];
  /** For lookup type */
  lookupType?:
    | string
    | undefined;
  /** Related entity type for lookup */
  relatedEntityType?:
    | string
    | undefined;
  /** For number type */
  min?: number | undefined;
  max?:
    | number
    | undefined;
  /** For string/text type */
  minLength?: number | undefined;
  maxLength?:
    | number
    | undefined;
  /** For array type */
  itemsType?:
    | string
    | undefined;
  /** Relationship value for lookup type */
  relationshipValue?: string | undefined;
}

/** Option for team choice fields */
export interface TeamFieldOption {
  label: string;
  value: string;
}

/** Operator definition for team fields */
export interface TeamFieldOperator {
  name: string;
  value: string;
}

export const GRPC_TEAMS_V1_PACKAGE_NAME = "grpc.teams.v1";

/** Teams service for teams app. */

export interface TeamsClient {
  /** Create a team */

  createTeam(request: CreateTeamRequest): Observable<CommonTeamResponse>;

  /** Update a team */

  updateTeam(request: UpdateTeamRequest): Observable<CommonTeamResponse>;

  /** Delete a team */

  deleteTeam(request: DeleteTeamRequest): Observable<EmptyResponse>;

  /** Get a team by ID */

  getTeamById(request: GetTeamByIdRequest): Observable<CommonTeamResponse>;

  /** Get all teams */

  getAllTeams(request: GetAllTeamsRequest): Observable<GetAllTeamsResponse>;

  /** Get all public teams */

  getAllPublicTeams(request: GetAllPublicTeamsRequest): Observable<GetAllPublicTeamsResponse>;

  /** Add a team member */

  addTeamMember(request: AddTeamMemberRequest): Observable<TeamMemberResponse>;

  /** Remove a team member */

  removeTeamMember(request: RemoveTeamMemberRequest): Observable<EmptyResponse>;

  /** Get all team members */

  getAllTeamMembers(request: GetAllTeamMembersRequest): Observable<GetAllTeamMembersResponse>;

  /** Get team configurations */

  getTeamConfigurations(request: GetTeamConfigurationsRequest): Observable<TeamConfigurationsResponse>;

  /** Update team configurations */

  updateTeamConfigurations(request: UpdateTeamConfigurationsRequest): Observable<TeamConfigurationsResponse>;

  /** Create a routing rule */

  createRoutingRule(request: CreateRoutingRuleRequest): Observable<CommonRoutingRuleResponse>;

  /** Update a routing rule */

  updateRoutingRule(request: UpdateRoutingRuleRequest): Observable<CommonRoutingRuleResponse>;

  /** Check team availability */

  checkTeamAvailability(request: CheckTeamAvailabilityRequest): Observable<CheckTeamAvailabilityResponse>;
}

/** Teams service for teams app. */

export interface TeamsController {
  /** Create a team */

  createTeam(
    request: CreateTeamRequest,
  ): Promise<CommonTeamResponse> | Observable<CommonTeamResponse> | CommonTeamResponse;

  /** Update a team */

  updateTeam(
    request: UpdateTeamRequest,
  ): Promise<CommonTeamResponse> | Observable<CommonTeamResponse> | CommonTeamResponse;

  /** Delete a team */

  deleteTeam(request: DeleteTeamRequest): Promise<EmptyResponse> | Observable<EmptyResponse> | EmptyResponse;

  /** Get a team by ID */

  getTeamById(
    request: GetTeamByIdRequest,
  ): Promise<CommonTeamResponse> | Observable<CommonTeamResponse> | CommonTeamResponse;

  /** Get all teams */

  getAllTeams(
    request: GetAllTeamsRequest,
  ): Promise<GetAllTeamsResponse> | Observable<GetAllTeamsResponse> | GetAllTeamsResponse;

  /** Get all public teams */

  getAllPublicTeams(
    request: GetAllPublicTeamsRequest,
  ): Promise<GetAllPublicTeamsResponse> | Observable<GetAllPublicTeamsResponse> | GetAllPublicTeamsResponse;

  /** Add a team member */

  addTeamMember(
    request: AddTeamMemberRequest,
  ): Promise<TeamMemberResponse> | Observable<TeamMemberResponse> | TeamMemberResponse;

  /** Remove a team member */

  removeTeamMember(
    request: RemoveTeamMemberRequest,
  ): Promise<EmptyResponse> | Observable<EmptyResponse> | EmptyResponse;

  /** Get all team members */

  getAllTeamMembers(
    request: GetAllTeamMembersRequest,
  ): Promise<GetAllTeamMembersResponse> | Observable<GetAllTeamMembersResponse> | GetAllTeamMembersResponse;

  /** Get team configurations */

  getTeamConfigurations(
    request: GetTeamConfigurationsRequest,
  ): Promise<TeamConfigurationsResponse> | Observable<TeamConfigurationsResponse> | TeamConfigurationsResponse;

  /** Update team configurations */

  updateTeamConfigurations(
    request: UpdateTeamConfigurationsRequest,
  ): Promise<TeamConfigurationsResponse> | Observable<TeamConfigurationsResponse> | TeamConfigurationsResponse;

  /** Create a routing rule */

  createRoutingRule(
    request: CreateRoutingRuleRequest,
  ): Promise<CommonRoutingRuleResponse> | Observable<CommonRoutingRuleResponse> | CommonRoutingRuleResponse;

  /** Update a routing rule */

  updateRoutingRule(
    request: UpdateRoutingRuleRequest,
  ): Promise<CommonRoutingRuleResponse> | Observable<CommonRoutingRuleResponse> | CommonRoutingRuleResponse;

  /** Check team availability */

  checkTeamAvailability(
    request: CheckTeamAvailabilityRequest,
  ): Promise<CheckTeamAvailabilityResponse> | Observable<CheckTeamAvailabilityResponse> | CheckTeamAvailabilityResponse;
}

export function TeamsControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "createTeam",
      "updateTeam",
      "deleteTeam",
      "getTeamById",
      "getAllTeams",
      "getAllPublicTeams",
      "addTeamMember",
      "removeTeamMember",
      "getAllTeamMembers",
      "getTeamConfigurations",
      "updateTeamConfigurations",
      "createRoutingRule",
      "updateRoutingRule",
      "checkTeamAvailability",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("Teams", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("Teams", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const TEAMS_SERVICE_NAME = "Teams";

/** Team Annotator service */

export interface TeamAnnotatorClient {
  /** Field Metadata APIs */

  getTeamFieldMetadata(request: GetTeamFieldMetadataRequest): Observable<GetTeamFieldMetadataResponse>;

  getTeamMemberFieldMetadata(
    request: GetTeamMemberFieldMetadataRequest,
  ): Observable<GetTeamMemberFieldMetadataResponse>;

  getTeamConfigurationFieldMetadata(
    request: GetTeamConfigurationFieldMetadataRequest,
  ): Observable<GetTeamConfigurationFieldMetadataResponse>;

  getTeamCapacityFieldMetadata(
    request: GetTeamCapacityFieldMetadataRequest,
  ): Observable<GetTeamCapacityFieldMetadataResponse>;

  getBusinessHoursConfigFieldMetadata(
    request: GetBusinessHoursConfigFieldMetadataRequest,
  ): Observable<GetBusinessHoursConfigFieldMetadataResponse>;

  /** Entity Data APIs */

  getTeamData(request: GetTeamDataRequest): Observable<GetTeamDataResponse>;

  getTeamMemberData(request: GetTeamMemberDataRequest): Observable<GetTeamMemberDataResponse>;

  getTeamConfigurationData(request: GetTeamConfigurationDataRequest): Observable<GetTeamConfigurationDataResponse>;

  getTeamCapacityData(request: GetTeamCapacityDataRequest): Observable<GetTeamCapacityDataResponse>;

  getBusinessHoursConfigData(
    request: GetBusinessHoursConfigDataRequest,
  ): Observable<GetBusinessHoursConfigDataResponse>;
}

/** Team Annotator service */

export interface TeamAnnotatorController {
  /** Field Metadata APIs */

  getTeamFieldMetadata(
    request: GetTeamFieldMetadataRequest,
  ): Promise<GetTeamFieldMetadataResponse> | Observable<GetTeamFieldMetadataResponse> | GetTeamFieldMetadataResponse;

  getTeamMemberFieldMetadata(
    request: GetTeamMemberFieldMetadataRequest,
  ):
    | Promise<GetTeamMemberFieldMetadataResponse>
    | Observable<GetTeamMemberFieldMetadataResponse>
    | GetTeamMemberFieldMetadataResponse;

  getTeamConfigurationFieldMetadata(
    request: GetTeamConfigurationFieldMetadataRequest,
  ):
    | Promise<GetTeamConfigurationFieldMetadataResponse>
    | Observable<GetTeamConfigurationFieldMetadataResponse>
    | GetTeamConfigurationFieldMetadataResponse;

  getTeamCapacityFieldMetadata(
    request: GetTeamCapacityFieldMetadataRequest,
  ):
    | Promise<GetTeamCapacityFieldMetadataResponse>
    | Observable<GetTeamCapacityFieldMetadataResponse>
    | GetTeamCapacityFieldMetadataResponse;

  getBusinessHoursConfigFieldMetadata(
    request: GetBusinessHoursConfigFieldMetadataRequest,
  ):
    | Promise<GetBusinessHoursConfigFieldMetadataResponse>
    | Observable<GetBusinessHoursConfigFieldMetadataResponse>
    | GetBusinessHoursConfigFieldMetadataResponse;

  /** Entity Data APIs */

  getTeamData(
    request: GetTeamDataRequest,
  ): Promise<GetTeamDataResponse> | Observable<GetTeamDataResponse> | GetTeamDataResponse;

  getTeamMemberData(
    request: GetTeamMemberDataRequest,
  ): Promise<GetTeamMemberDataResponse> | Observable<GetTeamMemberDataResponse> | GetTeamMemberDataResponse;

  getTeamConfigurationData(
    request: GetTeamConfigurationDataRequest,
  ):
    | Promise<GetTeamConfigurationDataResponse>
    | Observable<GetTeamConfigurationDataResponse>
    | GetTeamConfigurationDataResponse;

  getTeamCapacityData(
    request: GetTeamCapacityDataRequest,
  ): Promise<GetTeamCapacityDataResponse> | Observable<GetTeamCapacityDataResponse> | GetTeamCapacityDataResponse;

  getBusinessHoursConfigData(
    request: GetBusinessHoursConfigDataRequest,
  ):
    | Promise<GetBusinessHoursConfigDataResponse>
    | Observable<GetBusinessHoursConfigDataResponse>
    | GetBusinessHoursConfigDataResponse;
}

export function TeamAnnotatorControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getTeamFieldMetadata",
      "getTeamMemberFieldMetadata",
      "getTeamConfigurationFieldMetadata",
      "getTeamCapacityFieldMetadata",
      "getBusinessHoursConfigFieldMetadata",
      "getTeamData",
      "getTeamMemberData",
      "getTeamConfigurationData",
      "getTeamCapacityData",
      "getBusinessHoursConfigData",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("TeamAnnotator", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("TeamAnnotator", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const TEAM_ANNOTATOR_SERVICE_NAME = "TeamAnnotator";
