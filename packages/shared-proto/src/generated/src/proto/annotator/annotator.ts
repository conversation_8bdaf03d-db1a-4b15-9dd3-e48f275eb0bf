// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v3.20.3
// source: annotator/annotator.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.annotator.v1";

export interface GetEntityFieldMetadataRequest {
  entityType: string;
  relations: string[];
  teamId?: string | undefined;
  attributeType?: string | undefined;
}

/** Field metadata structure for entities */
export interface EntityFieldMetadata {
  /** Type of the field */
  type: string;
  /** Label for displaying */
  label: string;
  /** Description for displaying */
  description?:
    | string
    | undefined;
  /** Key of the field in findEntity api response */
  expression: string;
  /** If the field is a standard field */
  standard: boolean;
  /** If the field is enabled for SLA */
  slaEnabled?:
    | boolean
    | undefined;
  /** Constraints for the field */
  constraints?:
    | EntityFieldConstraints
    | undefined;
  /** Supported operators for the field */
  supportedOperators: EntityFieldOperator[];
  /** Nested fields */
  fields: { [key: string]: EntityFieldMetadata };
  /** Custom fields */
  customFields: CustomField[];
}

export interface EntityFieldMetadata_FieldsEntry {
  key: string;
  value: EntityFieldMetadata | undefined;
}

/** Field constraints for entities */
export interface EntityFieldConstraints {
  /** For choice type */
  dynamicOptions: EntityFieldOption[];
  /** Fixed options for choice type */
  options: EntityFieldOption[];
  /** For lookup type */
  lookupType?:
    | string
    | undefined;
  /** Related entity type for lookup */
  relatedEntityType?:
    | string
    | undefined;
  /** For number type */
  min?: number | undefined;
  max?:
    | number
    | undefined;
  /** For string/text type */
  minLength?: number | undefined;
  maxLength?:
    | number
    | undefined;
  /** For array type */
  itemsType?:
    | string
    | undefined;
  /** Relationship value for lookup type */
  relationshipValue?: string | undefined;
}

/** Option for entity choice fields */
export interface EntityFieldOption {
  label: string;
  value: string;
}

/** Operator definition for entity fields */
export interface EntityFieldOperator {
  name: string;
  value: string;
}

export interface CustomFieldOption {
  id: string;
  value: string;
  isDisabled: boolean;
  order: number;
}

export interface CustomField {
  id: string;
  name: string;
  description?: string | undefined;
  fieldType: string;
  options: CustomFieldOption[];
  placeholderText?: string | undefined;
  hintText?: string | undefined;
  defaultValue?: string | undefined;
  lookup?: string | undefined;
  mandatoryOnClose: boolean;
  mandatoryOnCreation: boolean;
  visibleToCustomer: boolean;
  editableByCustomer: boolean;
  /** JSON stringified object */
  metadata?:
    | string
    | undefined;
  /** JSON stringified object */
  validationRules?: string | undefined;
}

export interface GetEntityFieldMetadataResponse {
  fields: { [key: string]: EntityFieldMetadata };
  customFields: CustomField[];
}

export interface GetEntityFieldMetadataResponse_FieldsEntry {
  key: string;
  value: EntityFieldMetadata | undefined;
}

export interface GetEntityFieldInfoRequest {
  entityType: string;
  relations: string[];
  path: string;
}

export interface GetEntityFieldInfoResponse {
  exists: boolean;
  data: EntityFieldMetadata | undefined;
}

export interface GetEntityDataRequest {
  entityType: string;
  /** JSON stringified object containing the required fields to get entity data. Example: ticketId for ticket entity. */
  data: string;
  relations: string[];
}

export interface GetEntityDataResponse {
  data: string;
}

export interface FilterEntityDataRequest {
  entityType: string;
  /** JSON stringified object containing the required fields to filter entity data. Example: ticketId for ticket entity. */
  data: string;
  relations: string[];
}

export interface FilterEntityDataResponse {
  data: string;
}

export interface GetRequestSchemaForEntityDataRequest {
  entityType: string;
}

export interface GetRequestSchemaForEntityDataResponse {
  /** JSON parse it to get ajv formatted schema */
  data: string;
}

export const GRPC_ANNOTATOR_V1_PACKAGE_NAME = "grpc.annotator.v1";

export interface AnnotatorClient {
  getEntityFieldMetadata(request: GetEntityFieldMetadataRequest): Observable<GetEntityFieldMetadataResponse>;

  getEntityFieldInfo(request: GetEntityFieldInfoRequest): Observable<GetEntityFieldInfoResponse>;

  getRequestSchemaForEntityData(
    request: GetRequestSchemaForEntityDataRequest,
  ): Observable<GetRequestSchemaForEntityDataResponse>;

  getEntityData(request: GetEntityDataRequest): Observable<GetEntityDataResponse>;

  filterEntityData(request: FilterEntityDataRequest): Observable<FilterEntityDataResponse>;
}

export interface AnnotatorController {
  getEntityFieldMetadata(
    request: GetEntityFieldMetadataRequest,
  ):
    | Promise<GetEntityFieldMetadataResponse>
    | Observable<GetEntityFieldMetadataResponse>
    | GetEntityFieldMetadataResponse;

  getEntityFieldInfo(
    request: GetEntityFieldInfoRequest,
  ): Promise<GetEntityFieldInfoResponse> | Observable<GetEntityFieldInfoResponse> | GetEntityFieldInfoResponse;

  getRequestSchemaForEntityData(
    request: GetRequestSchemaForEntityDataRequest,
  ):
    | Promise<GetRequestSchemaForEntityDataResponse>
    | Observable<GetRequestSchemaForEntityDataResponse>
    | GetRequestSchemaForEntityDataResponse;

  getEntityData(
    request: GetEntityDataRequest,
  ): Promise<GetEntityDataResponse> | Observable<GetEntityDataResponse> | GetEntityDataResponse;

  filterEntityData(
    request: FilterEntityDataRequest,
  ): Promise<FilterEntityDataResponse> | Observable<FilterEntityDataResponse> | FilterEntityDataResponse;
}

export function AnnotatorControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getEntityFieldMetadata",
      "getEntityFieldInfo",
      "getRequestSchemaForEntityData",
      "getEntityData",
      "filterEntityData",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("Annotator", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("Annotator", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const ANNOTATOR_SERVICE_NAME = "Annotator";
