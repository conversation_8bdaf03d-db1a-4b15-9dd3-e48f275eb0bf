// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v3.20.3
// source: customfields/customfields.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.customfields.v1";

export interface Empty {
}

export interface FindAllCustomFieldsRequest {
  teamId?: string | undefined;
  source?: string | undefined;
  onlyTeamFields?: boolean | undefined;
  limit?: number | undefined;
  offset?: number | undefined;
}

export interface CustomFieldOption {
  id: string;
  value: string;
  isDisabled: boolean;
  order: number;
}

export interface CustomField {
  id: string;
  name: string;
  description?: string | undefined;
  fieldType: string;
  options: CustomFieldOption[];
  placeholderText?: string | undefined;
  hintText?: string | undefined;
  defaultValue?: string | undefined;
  lookup?: string | undefined;
  mandatoryOnClose: boolean;
  mandatoryOnCreation: boolean;
  visibleToCustomer: boolean;
  editableByCustomer: boolean;
  /** JSON stringified object */
  metadata?:
    | string
    | undefined;
  /** JSON stringified object */
  validationRules?: string | undefined;
}

export interface FindAllCustomFieldsResponse {
  results: CustomField[];
  total: number;
  offset: number;
}

export const GRPC_CUSTOMFIELDS_V1_PACKAGE_NAME = "grpc.customfields.v1";

export interface CustomFieldsServiceClient {
  findAllCustomFields(request: FindAllCustomFieldsRequest): Observable<FindAllCustomFieldsResponse>;
}

export interface CustomFieldsServiceController {
  findAllCustomFields(
    request: FindAllCustomFieldsRequest,
  ): Promise<FindAllCustomFieldsResponse> | Observable<FindAllCustomFieldsResponse> | FindAllCustomFieldsResponse;
}

export function CustomFieldsServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["findAllCustomFields"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("CustomFieldsService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("CustomFieldsService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const CUSTOM_FIELDS_SERVICE_NAME = "CustomFieldsService";
