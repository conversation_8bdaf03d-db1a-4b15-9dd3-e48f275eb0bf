// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v3.20.3
// source: organization/organization.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.organization.v1";

export interface GetDomainsRequest {
  /** The organization UID */
  orgUid: string;
}

export interface GetDomainsResponse {
  /** List of domains */
  domains: string[];
}

export interface UpdateOrganizationRequest {
  /** The name of the organization */
  name: string;
  /** The logo URL of the organization */
  logoUrl?: string | undefined;
}

export interface UpdateOrganizationResponse {
  /** The ID of the organization */
  id: string;
  /** The name of the organization */
  name: string;
  /** Whether the organization is verified */
  isVerified: boolean;
  /** The logo URL of the organization */
  logoUrl?: string | undefined;
}

/** Request for creating an organization */
export interface CreateOrganizationRequest {
  /** The name of the organization */
  name: string;
  /** The name of the user creating this organization */
  userName: string;
  /** The email of the user creating this organization */
  email: string;
  /** The password of the user creating this organization */
  password: string;
  /** The logo URL of the organization */
  logoUrl?: string | undefined;
}

export interface CreateOrganizationResponse {
  /** The ID of the organization */
  id: string;
  /** The name of the organization */
  name: string;
  /** Whether the organization is verified */
  isVerified: boolean;
  /** The logo URL of the organization */
  logoUrl?: string | undefined;
}

export const GRPC_ORGANIZATION_V1_PACKAGE_NAME = "grpc.organization.v1";

/** The organization service */

export interface OrganizationClient {
  /** Create an organization */

  createOrganization(request: CreateOrganizationRequest): Observable<CreateOrganizationResponse>;

  /** Update an organization */

  updateOrganization(request: UpdateOrganizationRequest): Observable<UpdateOrganizationResponse>;

  getDomains(request: GetDomainsRequest): Observable<GetDomainsResponse>;
}

/** The organization service */

export interface OrganizationController {
  /** Create an organization */

  createOrganization(
    request: CreateOrganizationRequest,
  ): Promise<CreateOrganizationResponse> | Observable<CreateOrganizationResponse> | CreateOrganizationResponse;

  /** Update an organization */

  updateOrganization(
    request: UpdateOrganizationRequest,
  ): Promise<UpdateOrganizationResponse> | Observable<UpdateOrganizationResponse> | UpdateOrganizationResponse;

  getDomains(
    request: GetDomainsRequest,
  ): Promise<GetDomainsResponse> | Observable<GetDomainsResponse> | GetDomainsResponse;
}

export function OrganizationControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["createOrganization", "updateOrganization", "getDomains"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("Organization", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("Organization", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const ORGANIZATION_SERVICE_NAME = "Organization";
