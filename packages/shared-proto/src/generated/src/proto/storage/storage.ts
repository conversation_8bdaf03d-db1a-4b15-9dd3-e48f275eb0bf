// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v3.20.3
// source: storage/storage.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.storage.v1";

/** Request message for uploading a file */
export interface UploadFileRequest {
  fileData: Uint8Array;
  fileName: string;
  contentType: string;
  /** Optional expiry format (1d, 1w, 1m, 1y) */
  expiry?: string | undefined;
}

/** Response message for uploading a file */
export interface UploadFileResponse {
  success: boolean;
  data: StorageFileData | undefined;
}

export interface StorageFileMetadata {
  privateUrl: string;
  publicUrl: string;
  expiresAt?: string | undefined;
}

/** Storage file data structure */
export interface StorageFileData {
  id: string;
  fileName: string;
  contentType: string;
  size: number;
  metadata: StorageFileMetadata | undefined;
}

export const GRPC_STORAGE_V1_PACKAGE_NAME = "grpc.storage.v1";

/** Storage service for file operations */

export interface StorageServiceClient {
  /** Upload a file to storage */

  uploadFile(request: UploadFileRequest): Observable<UploadFileResponse>;
}

/** Storage service for file operations */

export interface StorageServiceController {
  /** Upload a file to storage */

  uploadFile(
    request: UploadFileRequest,
  ): Promise<UploadFileResponse> | Observable<UploadFileResponse> | UploadFileResponse;
}

export function StorageServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["uploadFile"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("StorageService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("StorageService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const STORAGE_SERVICE_NAME = "StorageService";
