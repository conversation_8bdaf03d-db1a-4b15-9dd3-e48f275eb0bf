import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { AccountActivity } from "../../accounts/entities/account-activity.entity";
import { AccountNote } from "../../accounts/entities/account-note.entity";
import { AccountTask } from "../../accounts/entities/account-task.entity";
import { Account } from "../../accounts/entities/account.entity";
import { CustomerContact } from "../../accounts/entities/customer-contact.entity";
import {
  EntityType,
  SUPPORTED_TYPES,
} from "../../common/constants/annotator.constants";
import { FieldMetadata } from "../../common/decorators/field-metadata.decorator";
import { Organization } from "../../organizations/entities/organization.entity";
import { Storage } from "../../storage/entities/storage.entity";
import { Team } from "../../teams/entities/team.entity";
import { Ticket } from "../../tickets/entities/ticket.entity";
import { User } from "../../users/entities/user.entity";
import { CommentType, CommentVisibility } from "../constants";
import { Mentions } from "./mentions.entity";

export interface CommentMetadata {
  /**
   * This is a map of reaction name and the key to the count of reactions in Redis
   * ! Please don't abuse this field, it [MUST | SHOULD] ONLY be used for caching purposes
   */
  reactions: Record<string, { count: number; users: string[] }>;

  /**
   * This is an array of comment uid's which are direct replies to this comment
   */
  replies: string[];

  /**
   * This is an array of mention uid's in the comment
   */
  mentions: string[];

  /**
   * This is the source of the comment
   */
  source: string;

  /**
   * This is a flag to indicate that the comment should be ignored when forwarded
   * back to any integrated systems
   */
  ignoreSelf?: boolean;

  userReactions?: Array<{
    emojiId: string;
    emojiName: string;
    emojiUnicode: string;
    emojiUrl: string;
  }>;

  /**
   * This is the metadata for the integration that created the comment
   */
  integrationMetadata: Array<{
    /**
     * The name of the integration that created the comment
     */
    source: string;

    /**
     * The external id of the comment, this helps us identify the comment in the external system
     */
    externalId: string;

    /**
     * The channel id of the comment, this helps us identify the comment in the external system
     * on a specific channel that it was created on, channels are unique to each integration
     */
    channelId?: string;

    /**
     * The thread id of the comment, this helps us identify the comment in the external system
     * on a specific thread that it was created on, threads are unique to each integration
     */
    threadId?: string;
  }>;

  /**
   * This is a map of external metadata which can be used to store any additional information about the comment
   * this can also be exposed to the end user for storing additional information or external references
   */
  external_metadata: Record<string, any>;

  /**
   * This is a map of external sinks which can be used to store any additional information in an external app or sink
   */
  external_sinks: Record<string, any>;

  /**
   * Flag to indicate that Slack content updates should be skipped for this comment
   */
  skipSlackContentUpdate?: boolean;
}

@Entity("comments")
@Index(["uid", "organizationId"], { unique: true })
@Index(["ticketId", "organizationId", "teamId", "parentCommentId"])
@Index(["authorId", "organizationId"])
@Index(["commentVisibility", "createdAt"])
@Index("idx_comment_organization_lookup", [
  "ticket",
  "organizationId",
  "parentCommentId",
  "commentType",
  "commentVisibility",
])
export class Comment {
  @PrimaryGeneratedColumn({ type: "bigint" })
  id: string;

  @Column({ type: "text", default: () => "generate_ulid()" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Id",
    description: "Identifier of the comment",
    expression: "id",
    standard: true,
  })
  uid: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: "organization_id" })
  organization: Organization;

  @Index()
  @Column({ name: "organization_id" })
  organizationId: string;

  @Column("text")
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Id",
    description: "Content of the comment",
    expression: "content",
    standard: true,
  })
  content: string;

  @Column("text", { nullable: true, name: "content_json" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Content JSON stringified",
    description: "Content of the comment in JSON format",
    expression: "contentJson",
    standard: true,
  })
  contentJson: string;

  @Column("text", { nullable: true, name: "content_markdown" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Content Markdown",
    description: "Content of the comment in markdown format",
    expression: "contentMarkdown",
    standard: true,
  })
  contentMarkdown: string;

  @Column("text", { nullable: true, name: "content_html" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Content HTML",
    description: "Content of the comment in html format",
    expression: "contentHtml",
    standard: true,
  })
  contentHtml: string;

  @Column({ default: false, name: "is_edited" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.BOOLEAN,
    label: "Is Edited",
    description: "Whether the comment has been edited",
    expression: "isEdited",
    standard: true,
  })
  isEdited: boolean;

  @ManyToOne(() => Ticket)
  @JoinColumn({ name: "ticket_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Ticket",
    description: "Ticket associated with the comment",
    expression: "ticket",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.TICKET,
    },
  })
  ticket: Ticket;

  @Index()
  @Column({ nullable: true, name: "ticket_id" })
  ticketId: string;

  @ManyToOne(() => AccountNote)
  @JoinColumn({ name: "account_note_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Account Note",
    description: "Account note associated with the comment",
    expression: "accountNote",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.ACCOUNT_NOTE,
    },
  })
  accountNote: AccountNote;

  @Index()
  @Column({ nullable: true, name: "account_note_id" })
  accountNoteId: string;

  @ManyToOne(() => AccountActivity)
  @JoinColumn({ name: "account_activity_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Account Activity",
    description: "Account activity associated with the comment",
    expression: "accountActivity",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.ACCOUNT_ACTIVITY,
    },
  })
  accountActivity: AccountActivity;

  @Index()
  @Column({ nullable: true, name: "account_activity_id" })
  accountActivityId: string;

  @ManyToOne(() => AccountTask)
  @JoinColumn({ name: "account_task_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Account Task",
    description: "Account task associated with the comment",
    expression: "accountTask",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.ACCOUNT_TASK,
    },
  })
  accountTask: AccountTask;

  @Index()
  @Column({ nullable: true, name: "account_task_id" })
  accountTaskId: string;

  @ManyToOne(() => Team, { nullable: true })
  @JoinColumn({ name: "team_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Team",
    description: "Team associated with the comment",
    expression: "team",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.TEAM,
    },
  })
  team: Team;

  @Index()
  @Column({ nullable: true, name: "team_id" })
  teamId: string;

  @ManyToOne(() => Account)
  @JoinColumn({ name: "account_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Account",
    description: "Account associated with the comment",
    expression: "account",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.ACCOUNT,
    },
  })
  account: Account;

  @Index()
  @Column({ nullable: true, name: "account_id" })
  accountId: string;

  @Index()
  @Column({
    type: "enum",
    default: CommentVisibility.PRIVATE,
    name: "comment_visibility",
    enum: CommentVisibility,
  })
  @FieldMetadata({
    type: SUPPORTED_TYPES.CHOICE,
    label: "Comment Visibility",
    description: "Visibility of the comment",
    expression: "commentVisibility",
    standard: true,
    constraints: {
      options: Object.entries(CommentVisibility).map(([key, value]) => ({
        label:
          key[0].toUpperCase() + key.slice(1).toLowerCase().replace("_", " "),
        value: value,
      })),
    },
  })
  commentVisibility: CommentVisibility;

  @OneToMany(() => Mentions, (mention) => mention.comment)
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Mentions",
    description: "Mentions associated with the comment",
    expression: "mentions",
    standard: true,
    constraints: {
      lookupType: "manyToMany",
      relatedEntityType: EntityType.MENTIONS,
    },
  })
  mentions: Mentions[];

  @ManyToOne(() => User, { nullable: true, eager: true })
  @JoinColumn({ name: "author_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Author",
    description: "Author of the comment",
    expression: "author",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.USER,
    },
  })
  author: User;

  @Index()
  @Column({ nullable: true, name: "author_id" })
  authorId: string;

  @Index()
  @Column({ type: "bigint", nullable: true, name: "customer_contact_id" })
  customerContactId: string;

  @ManyToOne(() => CustomerContact, { nullable: true })
  @JoinColumn({ name: "customer_contact_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Customer Contact",
    description: "Customer contact associated with the comment",
    expression: "customerContact",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.CUSTOMER_CONTACT,
    },
  })
  customerContact: CustomerContact;

  @Index()
  @Column({
    type: "enum",
    enum: CommentType,
    default: CommentType.COMMENT,
    name: "comment_type",
  })
  @FieldMetadata({
    type: SUPPORTED_TYPES.CHOICE,
    label: "Comment Type",
    description: "Type of the comment",
    expression: "commentType",
    standard: true,
    constraints: {
      options: Object.entries(CommentType).map(([key, value]) => ({
        label:
          key[0].toUpperCase() + key.slice(1).toLowerCase().replace("_", " "),
        value: value,
      })),
    },
  })
  commentType: CommentType;

  @Column({ default: false, name: "is_pinned" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.BOOLEAN,
    label: "Is Pinned",
    description: "Whether the comment is pinned",
    expression: "isPinned",
    standard: true,
  })
  isPinned: boolean;

  @Column({ nullable: true, type: "varchar", name: "impersonated_user_email" })
  impersonatedUserEmail?: string;

  @Column({ nullable: true, type: "varchar", name: "impersonated_user_name" })
  impersonatedUserName: string;

  @Column({ nullable: true, type: "varchar", name: "impersonated_user_avatar" })
  impersonatedUserAvatar: string;

  @Column({ nullable: true, name: "source_email_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Source Email ID",
    description: "Source email ID of the comment",
    expression: "sourceEmailId",
    standard: true,
  })
  sourceEmailId: string; // For comments sourced from an email

  @Column({ nullable: true, type: "jsonb" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.OBJECT,
    label: "Metadata",
    description: "Metadata of the comment",
    expression: "metadata",
    standard: true,
  })
  metadata: CommentMetadata;

  @ManyToOne(() => Comment, (comment) => comment.directReplies)
  @JoinColumn({ name: "parent_comment_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Parent Comment",
    description: "Parent comment of the comment",
    expression: "parentComment",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.COMMENTS,
    },
  })
  // eslint-disable-next-line no-use-before-define
  parentComment: Comment;

  @Index()
  @Column({ nullable: true, name: "parent_comment_id" })
  parentCommentId: string;

  @Column({ nullable: true, name: "comment_thread_name" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Comment Thread Name",
    description: "Name of the comment thread",
    expression: "commentThreadName",
    standard: true,
  })
  commentThreadName: string;

  // All direct replies to this comment
  @OneToMany(() => Comment, (comment) => comment.parentComment)
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Direct Replies",
    description: "Direct replies to the comment",
    expression: "directReplies",
    standard: true,
    constraints: {
      lookupType: "manyToMany",
      relatedEntityType: EntityType.COMMENTS,
    },
  })
  // eslint-disable-next-line no-use-before-define
  directReplies: Comment[];

  @ManyToMany(() => Storage, (storage) => storage)
  @JoinTable({
    name: "comment_attachments",
    joinColumn: {
      name: "comment_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "storage_id",
      referencedColumnName: "id",
    },
  })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Attachments",
    description: "Attachments associated with the comment",
    expression: "attachments",
    standard: true,
    constraints: {
      lookupType: "manyToMany",
      relatedEntityType: EntityType.STORAGE,
    },
  })
  attachments: Storage[];

  @Index()
  @CreateDateColumn({ name: "created_at", type: "timestamptz" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.DATE,
    label: "Created At",
    description: "Date and time the comment was created",
    expression: "createdAt",
    standard: true,
  })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamptz" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.DATE,
    label: "Updated At",
    description: "Date and time the comment was updated",
    expression: "updatedAt",
    standard: true,
  })
  updatedAt: Date;

  @DeleteDateColumn({ name: "deleted_at", type: "timestamptz", nullable: true })
  deletedAt: Date;

  /**
   * Whether this comment is a top-level comment
   */
  isTopLevelComment(): boolean {
    return !this.parentCommentId;
  }
}
