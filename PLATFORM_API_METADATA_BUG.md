# Platform API Metadata Update Bug

## Issue Summary
The Platform API has a critical SQL bug in the comment metadata update functionality that causes "column 'key' does not exist" errors when trying to update comment metadata.

## Error Details
- **Error Message**: `column "key" does not exist`
- **Affected Endpoint**: `PATCH /v1/comments/{id}`
- **Affected Method**: `updateComment` in `comments.action.service.ts`
- **Impact**: Prevents Slack app from updating comment metadata for link tracking

## Root Cause
The bug is in the SQL query that performs JSONB metadata merging. The problematic code is located in:

**File**: `apps/platform/src/communications/services/comments.action.service.ts`
**Lines**: 355-358 and 411-414

### Problematic SQL Query
```sql
FROM (
  SELECT key FROM jsonb_object_keys(base)
  UNION
  SELECT key FROM jsonb_object_keys(new_data)
) AS all_keys(key)
```

### Issue Explanation
The problem is that PostgreSQL is interpreting `key` as a column reference instead of the result from `jsonb_object_keys()`. The subquery alias `(key)` is causing PostgreSQL to look for a column named `key` in the table, which doesn't exist.

## Fixes Required

### Fix #1: SQL Query Bug (Lines 355-358 and 411-414)
The SQL query needs to be corrected to properly alias the function results:

**Current (Broken) Code:**
```sql
FROM (
  SELECT key FROM jsonb_object_keys(base)
  UNION
  SELECT key FROM jsonb_object_keys(new_data)
) AS all_keys(key)
```

**Fixed Code:**
```sql
FROM (
  SELECT jsonb_object_keys(base) as key
  UNION
  SELECT jsonb_object_keys(new_data) as key
) AS all_keys
```

### Fix #2: commentThreadName Preservation (Line 384)
When updating only metadata, the platform API incorrectly sets `commentThreadName` to null.

**Current (Broken) Code:**
```typescript
.set({
  isEdited: true,
  commentThreadName: updateCommentDto.threadName ?? null,
  metadata: () => `...`
})
```

**Fixed Code:**
```typescript
.set({
  isEdited: true,
  // Only update commentThreadName if explicitly provided
  ...(updateCommentDto.threadName !== undefined && {
    commentThreadName: updateCommentDto.threadName,
  }),
  metadata: () => `...`
})
```

## Status Update
The Slack app has been updated to re-enable metadata updates with proper error handling. The functionality will work correctly once both platform API fixes are applied.

**File**: `src/slack/core/messages/triage.core.ts`
**Lines**: 621-648 - Re-enabled with try-catch error handling

**Additional Fixes Applied:**
- Fixed message changed handler to prevent crashes when related messages aren't found
- Added proper null safety for comment content in comment update handlers

## Impact Assessment
- **Severity**: Medium (non-critical feature)
- **Functionality Affected**: Slack thread link tracking in comment metadata
- **Core Features**: All core functionality (ticket creation, comments, triage messages) continues to work
- **User Experience**: No impact on end users

## Testing
Once the fix is implemented, test with:
1. Create a ticket from Slack
2. Verify that comment metadata updates work without errors
3. Check that Slack thread links are properly stored in comment metadata

## Files to Update in Platform API
1. `apps/platform/src/communications/services/comments.action.service.ts` - Fix SQL query
2. Add unit tests for metadata merging functionality
3. Add integration tests for comment metadata updates

## Files to Update in Slack App (After Platform Fix)
1. `src/slack/core/messages/triage.core.ts` - Re-enable updateCommentWithMetadata calls
2. Remove TODO comments and workaround code
