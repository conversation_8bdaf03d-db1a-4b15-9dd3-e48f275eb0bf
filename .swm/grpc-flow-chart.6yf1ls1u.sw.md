---
title: GRPC FLOW CHART
---

```mermaid
flowchart TB
    A[Environment Variables] --> B[ConfigSchema]
    B --> C[ConfigService]
    C --> D[GrpcConfig]
    D --> E1[userServiceOptions]
    D --> E2[workflowServiceOptions]
    D --> E3[serverOptions]
    E1 & E2 --> F[gRPC Client Connections]
    E3 --> G[gRPC Server]
    G --> H[ReflectionService]
```

<SwmMeta version="3.0.0" repo-id="Z2l0bGFiJTNBJTNBdGhlbmEtYmFja2VuZCUzQSUzQXQ4MjI3" repo-name="thena-backend"><sup>Powered by [Swimm](https://app.swimm.io/)</sup></SwmMeta>
