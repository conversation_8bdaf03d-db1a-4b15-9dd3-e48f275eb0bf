---
title: |
  Update user's business/working hours and timezone
---

Few things to note straight:

1. We utilize and reuse `business-hours-config.entity` here.
2. The Business Hours entity has both `user_id` and `team_id` column's set as `nullable` which means both `user` and `team` can reference it and set thier working hours.

### Steps involved to update a user's working hours

1. Update the timezone using the user's repository if provided
2. If the business hours are provided:
   1. First validate if the provided business hours are correct
   2. A top level validation is present using the DTO itself
   3. The <SwmToken path="/apps/platform/src/common/services/business-hours-validation.service.ts" pos="11:1:1" line-data="  validateBusinessHours(">`validateBusinessHours`</SwmToken> method does the rest where it will validate the rest of the conditions (refer to its documentation)
3. Once we're valid we'll use the <SwmToken path="/apps/platform/src/users/users.service.ts" pos="158:2:4" line-data="            ...this.generateBusinessHoursUpdates(businessHours),">`this.generateBusinessHoursUpdates`</SwmToken> to get the partial updates for business hours in users config.
4. We'll update the user's business hours using the `businessHoursConfigRepository` and purge the cache using <SwmToken path="/apps/platform/src/users/users.service.ts" pos="173:5:5" line-data="        await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(">`cachedBusinessHoursConfigRepository`</SwmToken>
5. Return the configuration back:
   1. Fetch user's details using user repository
   2. Fetch the business hours config using the cached repository

<SwmSnippet path="/apps/platform/src/users/users.service.ts" line="97">

---

Service method to update user's business hours

```typescript
  /**
   * Updates a user's business hours.
   * @param user The current user.
   * @param body The updates to perform on the user.
   * @returns A promise that resolves to the updated user and business hours configuration.
   */
  async updateBusinessHours(
    user: CurrentUser,
    body: UpdateTimezoneWorkingHoursDto,
  ) {
    const { timezone, businessHours } = body;

    // If no updates to perform on the user, throw an error
    if (!timezone && !businessHours) {
      throw new BadRequestException(
        "Updates to perform on the user were not provided!",
      );
    }

    // Find the user's business hours configuration
    const currentUser = await this.cachedUserRepository.findOneById(user.sub);
    if (!currentUser) {
      throw new NotFoundException("User not found!");
    }

    // If the business hours are being updated, validate them
    if (businessHours) {
      const tz = timezone || currentUser.timezone || "UTC";
      const validationResult =
        this.businessHoursValidationService.validateBusinessHours(
          businessHours,
          tz,
        );

      // If the business hours are invalid, throw an error
      if (!validationResult.isValid) {
        throw new BadRequestException(validationResult.error);
      }
    }

    // Check if the user already has a business hours configuration
    const existingBusinessHoursConfig =
      await this.cachedBusinessHoursConfigRepository.findByCondition({
        where: { user: { id: user.sub } },
      });

    // Update the user's timezone and business hours in a transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Update the user's timezone
      if (timezone) {
        await this.userRepository.updateWithTxn(
          txnContext,
          { id: user.sub },
          { timezone },
        );
      }

      // Update the user's business hours
      if (businessHours) {
        if (!existingBusinessHoursConfig) {
          await this.businessHoursConfigRepository.saveWithTxn(txnContext, {
            ...this.generateBusinessHoursUpdates(businessHours),
            user: currentUser,
            organizationId: user.orgId,
          });
        } else {
          await this.businessHoursConfigRepository.updateWithTxn(
            txnContext,
            {
              user: { id: user.sub },
            },
            this.generateBusinessHoursUpdates(businessHours),
          );
        }

        // Invalidate the user's business hours cache
        await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
          {
            userId: user.sub,
          },
        );
      }
    });

    // Find the updated user and business hours configuration
    const [updatedUser, businessHoursConfig] = await Promise.all([
      this.findOneByUserId(currentUser.uid),
      this.cachedBusinessHoursConfigRepository.findByCondition({
        where: { user: { id: user.sub } },
      }),
    ]);

    return {
      userId: currentUser.uid,
      user: updatedUser,
      businessHoursConfig,
    };
  }

  /**
   * Updates a user's workload.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @param updatePayload The payload to update the user's workload with.
   */
  public async updateUserWorkload(
    userId: string,
    orgId: string,
    updatePayload: UpdateUserWorkloadDto,
  ) {
    // Fetch the user
    const user = await this.cachedUserRepository.findByCondition({
      where: { id: userId, organizationId: orgId },
    });

    // If the user is not found, throw an error
    if (!user) {
      throw new NotFoundException("User not found!");
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      const updatedPayload = cloneDeep(user.metadata?.workload || {});
      updatedPayload[updatePayload.forTeamId] = {
        count: updatePayload.newTotalTickets,
      };

      // Update the user's workload
      await this.userRepository.updateWithTxn(
        txnContext,
        { id: userId },
        { metadata: { workload: updatedPayload } },
      );
    });
  }
```

---

</SwmSnippet>

## Description

This asynchronous method handles updating a user's business hours and timezone settings. It performs validation on the provided business hours, updates the relevant data in a transaction, and manages associated cache invalidation.

### Method Signature

```typescript
async updateBusinessHours(
    user: CurrentUser,
    body: UpdateTimezoneWorkingHoursDto
): Promise<{
    userId: string,
    user: User,
    businessHoursConfig: BusinessHoursConfig
}>
```

### Parameters

- user: <SwmToken path="/apps/platform/src/common/decorators/user.decorator.ts" pos="9:4:4" line-data="export interface CurrentUser {">`CurrentUser`</SwmToken> - The authenticated user making the request

  - Must contain a sub property identifying the user

- body: <SwmToken path="/apps/platform/src/common/dto/business-hours.dto.ts" pos="92:4:4" line-data="export class UpdateTimezoneWorkingHoursDto {">`UpdateTimezoneWorkingHoursDto`</SwmToken> - The update payload containing:

  - timezone?: string - Optional. The new timezone to set for the user

  - businessHours?: BusinessHours - Optional. The new business hours configuration

### Returns

Returns a Promise that resolves to an object containing:

- userId: string - The unique identifier of the updated user

- user: User - The complete updated user object

- businessHoursConfig: <SwmToken path="/packages/thena-platform-entities/src/business-hours/entities/business-hours-config.entity.ts" pos="35:4:4" line-data="export class BusinessHoursConfig {">`BusinessHoursConfig`</SwmToken> - The updated business hours configuration

### Throws

- BadRequestException

  - When neither timezone nor businessHours are provided

  - When provided business hours fail validation

- NotFoundException

  - When the user cannot be found in the system

### Business Logic

1. Validates that at least one update parameter (timezone or businessHours) is provided

2. Retrieves the current user from the cached repository

3. If updating business hours:

   - Validates the business hours against the specified timezone (or falls back to current/UTC)

   - Ensures the business hours meet all validation requirements

4. Executes updates within a transaction:

   - Updates timezone if provided

   - Updates business hours if provided

   - Invalidates business hours cache when hours are updated

5. Retrieves and returns the updated user and business hours configuration

### Example Usage

```typescript
const user = { sub: "user123" }; // CurrentUser object

const updates = {
  timezone: "America/New_York",

  businessHours: {
    // business hours configuration
  },
};

try {
  const result = await userService.updateBusinessHours(user, updates);

  console.log("Updated user:", result.user);

  console.log("New business hours:", result.businessHoursConfig);
} catch (error) {
  // Handle errors
}
```

### Dependencies

The method relies on several injected services:

- <SwmToken path="/apps/platform/src/users/users.service.ts" pos="32:3:3" line-data="    private cachedUserRepository: CachedUserRepository,">`cachedUserRepository`</SwmToken> - For user data retrieval

- <SwmToken path="/apps/platform/src/users/users.service.ts" pos="35:5:5" line-data="    private readonly businessHoursValidationService: BusinessHoursValidatorService,">`businessHoursValidationService`</SwmToken> - For validating business hours

- <SwmToken path="/apps/platform/src/users/users.service.ts" pos="144:5:5" line-data="    await this.transactionService.runInTransaction(async (txnContext) =&gt; {">`transactionService`</SwmToken> - For transaction management

- <SwmToken path="/packages/thena-platform-entities/src/users/repositories/user.repository.ts" pos="42:5:5" line-data="    private readonly userRepository: Repository&lt;User&gt;,">`userRepository`</SwmToken> - For user updates

- <SwmToken path="/packages/thena-platform-entities/src/business-hours/repositories/core/business-hours-config.repository.ts" pos="42:5:5" line-data="    private readonly businessHoursConfigRepository: Repository&lt;BusinessHoursConfig&gt;,">`businessHoursConfigRepository`</SwmToken> - For business hours configuration updates

- <SwmToken path="/apps/platform/src/users/users.service.ts" pos="173:5:5" line-data="        await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(">`cachedBusinessHoursConfigRepository`</SwmToken> - For managing business hours cache

### Notes

- All database operations are performed within a transaction to ensure data consistency

- Cache invalidation is performed automatically when business hours are updated

- The timezone will default to UTC if not specified and no previous timezone exists

<SwmMeta version="3.0.0" repo-id="Z2l0bGFiJTNBJTNBdGhlbmEtYmFja2VuZCUzQSUzQXQ4MjI3" repo-name="thena-backend"><sup>Powered by [Swimm](https://app.swimm.io/)</sup></SwmMeta>
