---
title: "App Entity "
---

This is the entity that is used to create the apps table. The manifest for the app will be a JSONB field inside the app because it needs flexiblity and can support new functionalities in the future. Changing the database schema whould require a lot of efforts compare to JSONB.&nbsp;

To ensure the manifest schema we will create and interface. The same interface would be used to validate the manifest as well.&nbsp;

- The App Manifest is an object where we have not added any interface. This check is happening in the create-app.dto.

<SwmSnippet path="/apps/apps-platform/src/database/app/entities/app.entity.ts" line="40" collapsed>

---

The developer ID will be a user.sub in the thena platform user entity table.

```typescript
  @Column({
    name: "developer_id",
    comment: "The ID of the developer of the app",
  })
  developerId: string;
```

---

</SwmSnippet>

<SwmSnippet path="/apps/apps-platform/src/database/app/entities/app.entity.ts" line="53" collapsed>

---

The visiblity indicates weather an app can be installed by users from another orginisation or the app is private scoped which mean only the user and its corresponding orginisation will be able to use it.&nbsp;

```typescript
  @Column({
    type: "enum",
    enum: AppVisibility,
    default: AppVisibility.PRIVATE,
    comment: "The current visibility of the app.",
  })
  visibility: AppVisibility;
```

---

</SwmSnippet>

<SwmMeta version="3.0.0" repo-id="Z2l0bGFiJTNBJTNBdGhlbmEtYmFja2VuZCUzQSUzQXQ4MjI3" repo-name="thena-backend"><sup>Powered by [Swimm](https://app.swimm.io/)</sup></SwmMeta>
