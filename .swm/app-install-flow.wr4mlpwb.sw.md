---
title: App Install Flow
---

```mermaid
flowchart TD
    Start([Start Installation]) --> Auth{Auth Check}
    Auth -- Invalid --> AuthErr[Return 401 Unauthorized]
    Auth -- Valid --> ValidateApp[Validate App Installation]

    subgraph Validation
        ValidateApp --> CheckApp{App Exists?}
        CheckApp -- No --> AppErr[Return 400 Bad Request]
        CheckApp -- Yes --> ValidateConfig[Validate Configuration]
        ValidateConfig --> CheckConfig{Config Valid?}
        CheckConfig -- No --> ConfigErr[Return 400 Bad Request]
        CheckConfig -- Yes --> CheckExisting{Check Existing Install}
        CheckExisting -- Exists --> ExistErr[Return 422 Already Installed]
    end

    CheckExisting -- Clear --> RegAct[Register Activities]
    RegAct --> ActErr{Activities Error?}
    ActErr -- Yes --> CompAct[Compensate Activities]
    CompAct --> ReturnActErr[Return 422 Activity Error]

    ActErr -- No --> RegEvents[Register Events]
    RegEvents --> EventErr{Events Error?}
    EventErr -- Yes --> CompEvents[Compensate Events]
    CompEvents --> ReturnEventErr[Return 422 Event Error]

    EventErr -- No --> CreateBot[Create Bot Installation]
    CreateBot --> BotErr{Bot Error?}
    BotErr -- Yes --> CompBot[Compensate Bot & Previous Steps]
    CompBot --> ReturnBotErr[Return 422 Bot Error]

    BotErr -- No --> SaveRecords[Save Installation Records]
    SaveRecords --> SaveErr{Save Error?}
    SaveErr -- Yes --> CompAll[Compensate All Steps]
    CompAll --> ReturnSaveErr[Return 422 Save Error]

    SaveErr -- No --> Success[Return Success Response]

    classDef errorNode fill:#ffecec,stroke:#ff0000
    classDef successNode fill:#e6ffe6,stroke:#008000
    classDef processNode fill:#e6f3ff,stroke:#0066cc
    class AuthErr,AppErr,ConfigErr,ExistErr,ReturnActErr,ReturnEventErr,ReturnBotErr,ReturnSaveErr errorNode
    class Success successNode
    class RegAct,RegEvents,CreateBot,SaveRecords processNode
```

<SwmMeta version="3.0.0" repo-id="Z2l0bGFiJTNBJTNBdGhlbmEtYmFja2VuZCUzQSUzQXQ4MjI3" repo-name="thena-backend"><sup>Powered by [Swimm](https://app.swimm.io/)</sup></SwmMeta>
