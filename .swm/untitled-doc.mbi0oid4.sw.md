---
title: Untitled doc
---

<SwmSnippet path="/apps/email/src/email-tickets/services/postmark-admin.services.ts" line="21">

---

**Purpose:** Handles incoming emails sent to your Postmark-managed email addresses.

**Use Case:** When a user sends an email to your support address (e.g., `<EMAIL>`), Postmark forwards the email details to your **Inbound Webhook URL**.

```typescript
        InboundHookUrl: emailConfig.inboundHookUrl,
```

---

</SwmSnippet>

```json
Example of an incoming message
{
  "From": "<EMAIL>",
  "To": "<EMAIL>",
  "Subject": "I need help",
  "TextBody": "My account is not working.",
  "Attachments": []
}

```

<SwmSnippet path="/apps/email/src/email-tickets/services/postmark-admin.services.ts" line="22">

---

- **Purpose:** Tracks the delivery status of emails **sent from Postmark**.

- **Use Case:** When Postmark sends an email (e.g., ticket notifications, password resets), this webhook notifies your system about delivery events.

- **Types of Events:**

  - **Delivered:** <PERSON>ail successfully reached the recipient.

  - **Bounced:** Email could not be delivered.

  - **Deferred:** Delivery is delayed.

  - **Spam Complaint:** Recipient marked your email as spam.

```typescript
        DeliveryHookUrl: emailConfig.deliveryHookUrl,
```

---

</SwmSnippet>

```
{
  "Recipient": "<EMAIL>",
  "DeliveredAt": "2025-02-04T10:00:00Z",
  "MessageID": "12345",
  "Status": "Delivered"
}
```

&nbsp;

&nbsp;

### Streams

&nbsp;

<SwmSnippet path="/apps/email/src/email-tickets/services/postmark-admin.services.ts" line="31">

---

### **Why Set Up Streams for Thena?**

Since you are: ✅ **Receiving** emails from customers (Inbound Stream)\
✅ **Sending** ticket notifications (Transactional Stream)

You need **both** streams:

- **Inbound Stream** → Handles incoming support emails (e.g., forwarded from `<EMAIL>`).

- **Transactional Stream** → Sends updates when tickets change (e.g., "Your ticket has been updated").

#### **How This Works in Your Case**

| Action                              | Stream Type              | Webhook Used                                                                                                                                                                                   |
| ----------------------------------- | ------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Customer emails `<EMAIL>`   | **Inbound Stream**       | <SwmToken path="/apps/email/src/email-tickets/services/postmark-admin.services.ts" pos="21:1:1" line-data="        InboundHookUrl: emailConfig.inboundHookUrl,">`InboundHookUrl`</SwmToken>    |
| Thena sends ticket updates to users | **Transactional Stream** | <SwmToken path="/apps/email/src/email-tickets/services/postmark-admin.services.ts" pos="22:1:1" line-data="        DeliveryHookUrl: emailConfig.deliveryHookUrl,">`DeliveryHookUrl`</SwmToken> |

```typescript
  async setupPostmarkStreams(serverId: string) {
```

---

</SwmSnippet>

<SwmMeta version="3.0.0" repo-id="Z2l0bGFiJTNBJTNBdGhlbmEtYmFja2VuZCUzQSUzQXQ4MjI3" repo-name="thena-backend"><sup>Powered by [Swimm](https://app.swimm.io/)</sup></SwmMeta>
