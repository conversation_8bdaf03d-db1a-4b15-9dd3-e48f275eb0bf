import asyncio
from typing import Any, Optional

import httpx
from fastapi import HTTPException

from .config import get_config
from .logging_config import get_logger

logger = get_logger(__name__)
config = get_config()

# Default Timeout for external API calls
DEFAULT_API_TIMEOUT = 30.0


class ExternalAPIsClient:
    """
    Singleton client for managing interactions with external APIs (Thena Platform, Exa).
    Reuses an httpx.AsyncClient for performance.
    """

    def __init__(self, timeout: float = DEFAULT_API_TIMEOUT) -> None:
        # Initialize the httpx client immediately
        self.http_client = httpx.AsyncClient(timeout=timeout)
        logger.info(
            "ExternalAPIsClient initialized with HTTP client.",
            props={"timeout": timeout},
        )

    async def close(self) -> None:
        """Closes the underlying httpx client."""
        if self.http_client:
            await self.http_client.aclose()
            logger.info("ExternalAPIsClient HTTP client closed.", props={})

    async def call_create_thena_ticket(
        self,
        thena_api_url: str,
        api_key: str,
        org_id: str,
        payload: dict[str, Any],
        x_request_source: Optional[str] = None,
    ) -> dict[str, Any]:
        """Makes an API call to create a Thena ticket."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        if x_request_source:
            headers["x-request-source"] = x_request_source

        url = f"{thena_api_url}/v1/tickets"
        try:
            response = await self.http_client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error creating Thena ticket: %s - %s",
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "payload": payload,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to create Thena ticket: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error creating Thena ticket: %s",
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "payload": payload,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error creating Thena ticket: {e}",
                props={"url": url, "payload": payload, "org_id": org_id},
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while creating the ticket.",
            ) from e

    async def call_create_thena_tickets_bulk(
        self,
        thena_api_url: str,
        api_key: str,
        org_id: str,
        tickets: list,
        options: Optional[dict] = None,
    ) -> dict[str, Any]:
        """Makes an API call to create multiple Thena tickets in bulk."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        
        # Prepare the payload with tickets and options
        payload = {"tickets": tickets}
        if options:
            payload["options"] = options
            
        url = f"{thena_api_url}/v1/tickets/bulk"
        try:
            response = await self.http_client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error creating bulk Thena tickets: %s - %s",
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "tickets_count": len(tickets),
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to create bulk Thena tickets: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error creating bulk Thena tickets: %s",
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "tickets_count": len(tickets),
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error creating bulk Thena tickets: {e}",
                props={"url": url, "tickets_count": len(tickets), "org_id": org_id},
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while creating bulk tickets.",
            ) from e
    
    async def call_get_all_accounts(
        self,
        thena_api_url: str,
        api_key: str,
        org_id: str,
        source: Optional[str] = None,
        status_id: Optional[str] = None,
        classification_id: Optional[str] = None,
        health_id: Optional[str] = None,
        industry_id: Optional[str] = None,
        owner_id: Optional[str] = None,
        page: Optional[int] = None,
        limit: Optional[int] = None,
    ) -> dict[str, Any]:
        """Makes an API call to get all accounts with optional filtering."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
        }
        
        # Build query parameters using dictionary comprehension
        param_mapping = {
            "source": source,
            "statusId": status_id,
            "classificationId": classification_id,
            "healthId": health_id,
            "industryId": industry_id,
            "ownerId": owner_id,
            "page": page,
            "limit": limit,
        }
        params = {key: value for key, value in param_mapping.items() if value is not None}
            
        url = f"{thena_api_url}/v1/accounts"
        try:
            response = await self.http_client.get(url, params=params, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error getting accounts: %s - %s",
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "params": params,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Error getting accounts: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error getting accounts: %s",
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "params": params,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                "Unexpected error getting accounts",
                props={"url": url, "params": params, "org_id": org_id},
                exc_info=True,
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while getting accounts.",
            ) from e
                
    async def call_get_account_notes(
        self,
        thena_api_url: str,
        api_key: str,
        org_id: str,
        account_id: Optional[str] = None,
        type: Optional[str] = None,
        page: Optional[int] = None,
        limit: Optional[int] = None,
    ) -> dict[str, Any]:
        """Makes an API call to get account notes by account ID and/or type."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
        }
        
        # Build query parameters using dictionary comprehension
        param_mapping = {
            "accountId": account_id,
            "type": type,
            "page": page,
            "limit": limit,
        }
        params = {key: value for key, value in param_mapping.items() if value is not None}
            
        url = f"{thena_api_url}/v1/accounts/notes"
        try:
            response = await self.http_client.get(url, params=params, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error getting account notes: %s - %s",
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "params": params,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Error getting account notes: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error getting account notes: %s",
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "params": params,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                "Unexpected error getting account notes",
                props={"url": url, "params": params, "org_id": org_id},
                exc_info=True,
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while getting account notes.",
            ) from e
            
    async def call_create_account_note(
        self,
        thena_api_url: str,
        api_key: str,
        org_id: str,
        account_id: str,
        content: str,
        type: Optional[str] = None,
        visibility: Optional[str] = None,
        attachment_urls: Optional[list[str]] = None,
    ) -> dict[str, Any]:
        """Makes an API call to create an account note."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        
        # Prepare the request payload
        payload = {
            "accountId": account_id,
            "content": content,
        }
        
        # Add optional parameters if provided
        if type:
            payload["type"] = type
        if visibility:
            payload["visibility"] = visibility
        if attachment_urls:
            payload["attachmentUrls"] = attachment_urls
            
        url = f"{thena_api_url}/v1/accounts/notes"
        try:
            response = await self.http_client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error creating account note: %s - %s",
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "account_id": account_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Error creating account note: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error creating account note: %s",
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "account_id": account_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error creating account note: {e}",
                props={"url": url, "account_id": account_id, "org_id": org_id},
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while creating the account note.",
            ) from e
            
    async def call_update_account_note(
        self,
        thena_api_url: str,
        api_key: str,
        org_id: str,
        note_id: str,
        content: Optional[str] = None,
        type: Optional[str] = None,
        visibility: Optional[str] = None,
        attachment_urls: Optional[list[str]] = None,
    ) -> dict[str, Any]:
        """Makes an API call to update an account note."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        
        # Prepare the request payload with only the fields that are being updated
        payload = {}
        if content is not None:
            payload["content"] = content
        if type is not None:
            payload["type"] = type
        if visibility is not None:
            payload["visibility"] = visibility
        if attachment_urls is not None:
            payload["attachmentUrls"] = attachment_urls
            
        # If no fields to update, raise an error
        if not payload:
            raise HTTPException(
                status_code=400,
                detail="At least one field (content, type, visibility, or attachment_urls) must be provided for update",
            )
            
        url = f"{thena_api_url}/v1/accounts/notes/{note_id}"
        try:
            response = await self.http_client.put(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error updating account note: %s - %s",
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "note_id": note_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Error updating account note: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error updating account note: %s",
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "note_id": note_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                "Unexpected error updating account note",
                props={"url": url, "note_id": note_id, "org_id": org_id},
                exc_info=True,
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while updating account note.",
            ) from e
    
    async def call_create_thena_comment(
        self,
        thena_api_url: str,
        ticket_id: str,
        api_key: str,
        org_id: str,
        payload: dict[str, Any],
    ) -> dict[str, Any]:
        """Makes an API call to create a Thena comment."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        url = f"{thena_api_url}/v1/tickets/{ticket_id}/comment"
        try:
            response = await self.http_client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error creating Thena comment for ticket %s: %s - %s",
                ticket_id,
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "payload": payload,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to create Thena comment: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error creating Thena comment for ticket %s: %s",
                ticket_id,
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "payload": payload,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error creating Thena comment for ticket {ticket_id}: {e}",
                props={
                    "url": url,
                    "payload": payload,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while creating the comment.",
            ) from e

    async def call_update_thena_ticket(
        self,
        thena_api_url: str,
        ticket_id: str,
        api_key: str,
        org_id: str,
        payload: dict[str, Any],
    ) -> dict[str, Any]:
        """Makes an API call to update a Thena ticket."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        url = f"{thena_api_url}/v1/tickets/{ticket_id}"
        try:
            response = await self.http_client.patch(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error updating Thena ticket %s: %s - %s",
                ticket_id,
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "payload": payload,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to update Thena ticket: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error updating Thena ticket %s: %s",
                ticket_id,
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "payload": payload,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error updating Thena ticket {ticket_id}: {e}",
                props={
                    "url": url,
                },
            )
            raise HTTPException(
                status_code=500, detail=f"Unexpected error updating Thena ticket: {e}"
            ) from e
            
    async def call_update_thena_tickets_bulk(
        self,
        thena_api_url: str,
        api_key: str,
        org_id: str,
        payload: dict[str, Any],
    ) -> dict[str, Any]:
        """Makes an API call to update multiple Thena tickets in bulk."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        url = f"{thena_api_url}/v1/tickets/bulk"
        try:
            response = await self.http_client.patch(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error updating Thena tickets in bulk: %s - %s",
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "payload": payload,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to update Thena tickets in bulk: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error updating Thena tickets in bulk: %s",
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "payload": payload,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error updating Thena tickets in bulk: {e}",
                props={
                    "url": url,
                    "payload": payload,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=500, detail=f"Unexpected error updating Thena tickets in bulk: {e}"
            ) from e
            
    async def call_add_tags_to_ticket(
        self,
        thena_api_url: str,
        ticket_id: str,
        api_key: str,
        org_id: str,
        tag_ids: list[str],
    ) -> dict[str, Any]:
        """Makes an API call to add tags to a Thena ticket."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        url = f"{thena_api_url}/v1/tickets/{ticket_id}/tags"
        payload = {"tagIds": tag_ids}
        
        try:
            response = await self.http_client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error adding tags to Thena ticket %s: %s - %s",
                ticket_id,
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "tag_ids": tag_ids,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to add tags to Thena ticket: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error adding tags to Thena ticket %s: %s",
                ticket_id,
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "tag_ids": tag_ids,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error adding tags to Thena ticket {ticket_id}: {e}",
                props={
                    "url": url,
                    "payload": payload,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=500, detail=f"Unexpected error adding tags to Thena ticket: {e}"
            ) from e
            
    async def call_create_team_tag(
        self,
        thena_api_url: str,
        team_id: str,
        api_key: str,
        org_id: str,
        name: str,
        color: str,
        tag_type: str = "ticket",
        description: Optional[str] = None,
    ) -> dict[str, Any]:
        """Makes an API call to create a new tag for a team."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        url = f"{thena_api_url}/v1/teams/{team_id}/tags"
        
        # Prepare payload
        payload = {
            "name": name,
            "color": color,
        }
        
        if description is not None:
            payload["description"] = description
        
        try:
            response = await self.http_client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error creating tag for team %s: %s - %s",
                team_id,
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "payload": payload,
                    "team_id": team_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to create tag for team: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error creating tag for team %s: %s",
                team_id,
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "payload": payload,
                    "team_id": team_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error creating tag for team {team_id}: {e}",
                props={
                    "url": url,
                    "payload": payload,
                    "team_id": team_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=500, detail=f"Unexpected error creating tag for team: {e}"
            ) from e
            
    async def call_get_team_tags(
        self,
        thena_api_url: str,
        team_id: str,
        api_key: str,
        org_id: str,
    ) -> dict[str, Any]:
        """Makes an API call to get all tags for a team."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        url = f"{thena_api_url}/v1/teams/{team_id}/tags"
        
        try:
            response = await self.http_client.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error getting tags for team %s: %s - %s",
                team_id,
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "team_id": team_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to get tags for team: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error getting tags for team %s: %s",
                team_id,
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "team_id": team_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error getting tags for team {team_id}: {e}",
                props={
                    "url": url,
                    "team_id": team_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error getting tags for team: {e}",
            ) from e
            
    async def call_archive_ticket(
        self,
        thena_api_url: str,
        ticket_id: str,
        api_key: str,
        org_id: str,
    ) -> dict[str, Any]:
        """Makes an API call to archive a Thena ticket."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
        }
        url = f"{thena_api_url}/v1/tickets/{ticket_id}/archive"
        
        try:
            # Use PATCH method with an empty JSON body to avoid 400 error
            response = await self.http_client.patch(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error archiving Thena ticket %s: %s - %s",
                ticket_id,
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to archive ticket: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error archiving Thena ticket %s: %s",
                ticket_id,
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error archiving Thena ticket {ticket_id}: {e}",
                props={
                    "url": url,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error archiving ticket: {e}",
            ) from e
            
    async def call_archive_tickets_bulk(
        self,
        thena_api_url: str,
        ticket_ids: list[str],
        api_key: str,
        org_id: str,
    ) -> dict[str, Any]:
        """Makes an API call to archive multiple Thena tickets in bulk."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
            "Content-Type": "application/json",
        }
        url = f"{thena_api_url}/v1/tickets/bulk/archive"
        
        # Prepare payload
        payload = {
            "ticketIds": ticket_ids
        }
        
        try:
            # Use PATCH method for bulk archive to match the single archive endpoint
            response = await self.http_client.patch(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error archiving Thena tickets in bulk: %s - %s",
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "ticket_count": len(ticket_ids),
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to archive tickets in bulk: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error archiving Thena tickets in bulk: %s",
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "ticket_count": len(ticket_ids),
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                "Unexpected error archiving Thena tickets in bulk: %s",
                str(e),
                props={
                    "url": url,
                    "ticket_count": len(ticket_ids),
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error archiving tickets in bulk: {str(e)}",
            ) from e

    async def call_get_thena_ticket(
        self, thena_api_url: str, ticket_id: str, api_key: str, org_id: str
    ) -> dict[str, Any]:
        """Makes an API call to get a Thena ticket."""
        headers = {
            "x-api-key": api_key,
            "x-org-id": org_id,
        }
        url = f"{thena_api_url}/v1/tickets/{ticket_id}"
        try:
            response = await self.http_client.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error getting Thena ticket %s: %s - %s",
                ticket_id,
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to get Thena ticket: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error getting Thena ticket %s: %s",
                ticket_id,
                str(e),
                props={
                    "url": url,
                    "error": str(e),
                    "ticket_id": ticket_id,
                    "org_id": org_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Thena platform: {e}",
            ) from e
        except Exception as e:
            logger.exception(
                "Unexpected error getting Thena ticket",
                props={"url": url, "ticket_id": ticket_id, "org_id": org_id},
                exc_info=True,
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while getting the ticket.",
            ) from e

    async def call_exa_search(
        self, exa_api_url: str, exa_api_key: str, payload: dict[str, Any],
    ) -> dict[str, Any]:
        """Makes an API call to the Exa search endpoint."""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-api-key": exa_api_key,
        }
        url = f"{exa_api_url}/search"
        try:
            response = await self.http_client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error during Exa search: %s - %s",
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "payload": payload,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed during Exa search: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error during Exa search: %s",
                str(e),
                props={"url": url, "error": str(e), "payload": payload},
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Exa search API: {e}"
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error during Exa search: {e}",
                props={"url": url, "payload": payload},
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred during Exa search.",
            ) from e

    async def call_get_exa_content(
        self, exa_api_url: str, exa_api_key: str, result_id: str
    ) -> dict[str, Any]:
        """Makes an API call to the Exa content endpoint."""
        headers = {
            "Accept": "application/json",
            "x-api-key": exa_api_key,
        }
        url = f"{exa_api_url}/content"
        params = {"id": result_id}
        try:
            response = await self.http_client.get(url, params=params, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.exception(
                "HTTP error getting Exa content for ID %s: %s - %s",
                result_id,
                e.response.status_code,
                e.response.text,
                props={
                    "url": url,
                    "params": params,
                    "status_code": e.response.status_code,
                    "response": e.response.text,
                    "result_id": result_id,
                },
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Failed to get Exa content: {e.response.text}",
            ) from e
        except httpx.RequestError as e:
            logger.exception(
                "Request error getting Exa content for ID %s: %s",
                result_id,
                str(e),
                props={
                    "url": url,
                    "params": params,
                    "error": str(e),
                    "result_id": result_id,
                },
            )
            raise HTTPException(
                status_code=503, detail=f"Error connecting to Exa content API: {e}"
            ) from e
        except Exception as e:
            logger.exception(
                f"Unexpected error getting Exa content for ID {result_id}: {e}",
                props={"url": url, "params": params, "result_id": result_id},
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred during Exa content retrieval.",
            ) from e


# --- Singleton Implementation ---
_client_instance: Optional[ExternalAPIsClient] = None
_init_lock = asyncio.Lock()


async def get_external_apis_client() -> ExternalAPIsClient:
    """
    Get the singleton instance of the ExternalAPIsClient.
    Initializes the client on first call.
    """
    global _client_instance, _init_lock
    if _client_instance is None:
        async with _init_lock:
            # Check again inside the lock in case it was created while waiting
            if _client_instance is None:
                logger.info(
                    "Creating new ExternalAPIsClient singleton instance.", props={}
                )
                _client_instance = ExternalAPIsClient(timeout=DEFAULT_API_TIMEOUT)
                # No async initialize needed here as client is created in __init__
    return _client_instance


async def close_external_apis_client():
    """Closes the singleton ExternalAPIsClient instance during shutdown."""
    global _client_instance
    if _client_instance:
        logger.info("Closing singleton ExternalAPIsClient instance.", props={})
        await _client_instance.close()
        _client_instance = None
