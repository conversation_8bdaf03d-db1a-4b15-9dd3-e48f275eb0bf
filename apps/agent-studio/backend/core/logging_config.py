import json
import logging
import logging.config
import os
import sys
import time
import uuid
from contextvars import Context<PERSON><PERSON>
from typing import Any, Dict


# Check if we're running in a test environment
def is_test_environment():
    """Check if we're running in a test environment."""
    return "pytest" in sys.modules or os.environ.get("TESTING") == "1"


# Context variable to store correlation ID
correlation_id_context: ContextVar[str] = ContextVar("correlation_id", default="")


def get_correlation_id() -> str:
    """Get the current correlation ID or generate a new one if not set."""
    corr_id = correlation_id_context.get()
    if not corr_id:
        corr_id = str(uuid.uuid4())
        correlation_id_context.set(corr_id)
    return corr_id


def set_correlation_id(correlation_id: str) -> None:
    """Set the correlation ID for the current context."""
    correlation_id_context.set(correlation_id)


# For structured logging
class JsonFormatter(logging.Formatter):
    """
    Formatter that outputs JSON strings after parsing the log record.
    """

    def __init__(self, **kwargs):
        self.json_default = kwargs.pop("json_default", str)
        self.json_encoder = kwargs.pop("json_encoder", json.JSONEncoder)
        super().__init__(**kwargs)

    def format(self, record):
        log_record: Dict[str, Any] = {}

        # Standard log record attributes
        log_record["level"] = record.levelname
        log_record["time"] = self.formatTime(record)
        log_record["name"] = record.name
        log_record["message"] = record.getMessage()

        # Add correlation ID
        if not hasattr(record, "correlation_id"):
            record.correlation_id = get_correlation_id()
        log_record["correlation_id"] = record.correlation_id

        # Add timestamp in ISO format
        log_record["timestamp"] = time.strftime("%Y-%m-%dT%H:%M:%S.000Z", time.gmtime())

        # Add exception info if available
        if record.exc_info:
            log_record["exception"] = self.formatException(record.exc_info)

        # Add extra fields from the record
        if hasattr(record, "props"):
            try:
                log_record.update(record.props)
            except (TypeError, ValueError) as e:
                log_record["props_error"] = f"Error adding props: {str(e)}"

        # Add any extra attributes that were passed in
        if hasattr(record, "_dict_"):
            for key, value in record._dict_.items():
                if key not in [
                    "levelname",
                    "asctime",
                    "name",
                    "msg",
                    "args",
                    "exc_info",
                    "exc_text",
                    "stack_info",
                    "lineno",
                    "funcName",
                    "created",
                    "msecs",
                    "relativeCreated",
                    "levelno",
                    "pathname",
                    "filename",
                    "module",
                    "sinfo",
                    "props",
                    "correlation_id",
                ]:
                    try:
                        log_record[key] = value
                    except Exception:
                        log_record[f"{key}_error"] = "Error adding attribute"

        try:
            return json.dumps(
                log_record, default=self.json_default, cls=self.json_encoder
            )
        except (TypeError, ValueError, OverflowError) as e:
            # Fallback to a simpler format if JSON serialization fails
            return json.dumps(
                {
                    "level": record.levelname,
                    "time": self.formatTime(record),
                    "name": record.name,
                    "message": record.getMessage(),
                    "correlation_id": record.correlation_id,
                    "serialization_error": str(e),
                }
            )


class CorrelationIdFilter(logging.Filter):
    """Filter that adds correlation ID to log records."""

    def filter(self, record):
        if not hasattr(record, "correlation_id"):
            record.correlation_id = get_correlation_id()
        return True


# Custom logger adapter to add context and correlation IDs
class ContextLoggerAdapter(logging.LoggerAdapter):
    """
    Logger adapter that adds context information and correlation IDs to log records.
    """

    def __init__(self, logger, service_name, environment):
        super().__init__(logger, {})
        self.service_name = service_name
        self.environment = environment
        # Forward level attribute from the underlying logger
        self.level = logger.level

    def process(self, msg, kwargs):
        # Add service and environment to extra
        kwargs.setdefault("extra", {}).update(
            {
                "service": self.service_name,
                "environment": self.environment,
            }
        )

        # Handle props if provided
        if "props" in kwargs:
            props = kwargs.pop("props")
            kwargs["extra"]["props"] = props

        # Add correlation ID if not present
        if "correlation_id" not in kwargs.get("extra", {}):
            kwargs.setdefault("extra", {})["correlation_id"] = get_correlation_id()

        return msg, kwargs

    # Forward these properties and methods to the underlying logger
    @property
    def name(self):
        return self.logger.name

    @property
    def levelno(self):
        return self.logger.levelno

    def isEnabledFor(self, level):
        return self.logger.isEnabledFor(level)

    def getEffectiveLevel(self):
        return self.logger.getEffectiveLevel()

    def setLevel(self, level):
        self.logger.setLevel(level)
        self.level = self.logger.level

    # Add methods needed for pytest compatibility
    def addHandler(self, hdlr):
        """Forward addHandler to the underlying logger for pytest compatibility."""
        return self.logger.addHandler(hdlr)

    def removeHandler(self, hdlr):
        """Forward removeHandler to the underlying logger for pytest compatibility."""
        return self.logger.removeHandler(hdlr)

    @property
    def handlers(self):
        """Forward handlers property to the underlying logger for pytest compatibility."""
        return self.logger.handlers

    @property
    def propagate(self):
        """Forward propagate property to the underlying logger for pytest compatibility."""
        return self.logger.propagate

    @propagate.setter
    def propagate(self, value):
        """Forward propagate setter to the underlying logger for pytest compatibility."""
        self.logger.propagate = value


def configure_logging(
    log_level: str = "INFO",
    env: str = "development",
    service_name: str = "thena-agent-studio",
) -> None:
    """
    Configure logging for the application.

    Args:
        log_level: The log level to use (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        env: The environment (development, staging, production)
        service_name: The name of the service
    """
    # Determine if we're in production
    is_production = env.lower() == "production"

    # Base config
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": JsonFormatter,
            },
            "standard": {
                "format": "%(asctime)s - [%(correlation_id)s] - %(name)s - %(levelname)s - %(message)s"
            },
        },
        "filters": {
            "correlation_id": {
                "()": CorrelationIdFilter,
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": log_level,
                "formatter": "json" if is_production else "standard",
                "stream": sys.stdout,
                "filters": ["correlation_id"],
            },
        },
        "loggers": {
            "": {  # Root logger
                "handlers": ["console"],
                "level": log_level,
                "propagate": False,
            },
            # Add specific loggers with different levels if needed
            "backend": {
                "handlers": ["console"],
                "level": log_level,
                "propagate": False,
            },
            "uvicorn": {
                "handlers": ["console"],
                "level": log_level,
                "propagate": False,
            },
        },
    }

    # Apply the configuration
    logging.config.dictConfig(config)

    # Store the original getLogger function
    original_get_logger = logging.getLogger

    # Create a new getLogger function that returns an adapter
    def get_logger_with_context(name=None):
        logger = original_get_logger(name)
        return ContextLoggerAdapter(logger, service_name, env)

    # Replace the getLogger function, but only if we're not in a test environment
    if not is_test_environment():
        logging.getLogger = get_logger_with_context

    # Log that logging has been configured
    logger = get_logger_with_context(__name__)
    logger.info(
        "Logging configured", props={"log_level": log_level, "environment": env}
    )


# FastAPI middleware for correlation ID
class CorrelationIdMiddleware:
    """Middleware to extract or generate correlation ID for each request."""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            return await self.app(scope, receive, send)

        # Extract request headers
        headers = dict(scope.get("headers", []))

        # Try to get correlation ID from headers
        correlation_id = None
        if b"x-correlation-id" in headers:
            correlation_id = headers[b"x-correlation-id"].decode("utf-8")

        # If not found, generate a new one
        if not correlation_id:
            correlation_id = str(uuid.uuid4())

        # Set correlation ID in context
        token = correlation_id_context.set(correlation_id)

        # Modify the send function to include correlation ID in response headers
        original_send = send

        async def send_with_correlation_id(message):
            if message["type"] == "http.response.start":
                # Add correlation ID to response headers
                headers = message.get("headers", [])
                headers.append((b"x-correlation-id", correlation_id.encode("utf-8")))
                message["headers"] = headers

            await original_send(message)

        try:
            # Process the request with the correlation ID set
            await self.app(scope, receive, send_with_correlation_id)
        finally:
            # Reset the context
            correlation_id_context.reset(token)


# Helper function to get a logger
def get_logger(name: str) -> logging.LoggerAdapter:
    """Get a logger with the given name."""
    from .config import get_config

    config = get_config()

    # If we're in a test environment, use a simpler logger setup
    if is_test_environment():
        logger = logging.getLogger(name)
        # Only wrap in ContextLoggerAdapter if it's not already wrapped
        if not isinstance(logger, ContextLoggerAdapter):
            return ContextLoggerAdapter(
                logger, "thena-agent-studio", config.ENVIRONMENT
            )
        return logger

    # Normal application logging
    logger = logging.getLogger(name)
    return ContextLoggerAdapter(logger, "thena-agent-studio", config.ENVIRONMENT)
