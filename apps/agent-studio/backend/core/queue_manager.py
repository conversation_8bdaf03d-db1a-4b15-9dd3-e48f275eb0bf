"""
Queue manager for background task processing using RQ (Redis Queue).
Integrates with the existing RedisConnectionManager.
"""

import uuid
from typing import Callable, Dict, Optional

import redis
import rq
from rq.job import Job

from ..utils.logger import get_logger
from .redis_manager import RedisConnectionManager

logger = get_logger("queue_manager")

# Define queue names
DEFAULT_QUEUE = "default"
HIGH_PRIORITY_QUEUE = "high"
LOW_PRIORITY_QUEUE = "low"
FILE_PROCESSING_QUEUE = "file_processing"
FLOW_PROCESSING_QUEUE = "flow_processing"


class QueueManager:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "initialized"):
            self.redis_manager = RedisConnectionManager()
            self.redis_conn = None
            self.queues = {}
            self.initialized = False

    async def initialize(self):
        """Initialize the queue manager with Redis connection."""
        if self.initialized:
            return

        try:
            # Get a synchronous Redis connection from the async Redis manager
            # This is needed because RQ requires a synchronous Redis client
            redis_url = await self._get_redis_url()

            # Create a synchronous Redis connection for RQ
            self.redis_conn = redis.from_url(redis_url, decode_responses=True)

            # Create queues
            self.queues = {
                DEFAULT_QUEUE: rq.Queue(DEFAULT_QUEUE, connection=self.redis_conn),
                HIGH_PRIORITY_QUEUE: rq.Queue(
                    HIGH_PRIORITY_QUEUE, connection=self.redis_conn
                ),
                LOW_PRIORITY_QUEUE: rq.Queue(
                    LOW_PRIORITY_QUEUE, connection=self.redis_conn
                ),
                FILE_PROCESSING_QUEUE: rq.Queue(
                    FILE_PROCESSING_QUEUE, connection=self.redis_conn
                ),
                FLOW_PROCESSING_QUEUE: rq.Queue(
                    FLOW_PROCESSING_QUEUE, connection=self.redis_conn
                ),
            }

            self.initialized = True
            logger.info(
                "Queue manager initialized successfully",
                props={"queues": list(self.queues.keys())},
            )
        except Exception as e:
            logger.error(f"Failed to initialize queue manager: {str(e)}")
            raise

    async def _get_redis_url(self) -> str:
        """Get Redis URL from environment variables."""
        from ..core.config import get_config

        config = get_config()

        # Construct Redis URL from config
        redis_url = f"redis://"

        # Add password if provided
        if config.REDIS_PASSWORD:
            redis_url += f":{config.REDIS_PASSWORD}@"

        # Add host and port
        redis_url += f"{config.REDIS_HOST or 'localhost'}:{config.REDIS_PORT or 6379}"

        # Add database number
        redis_url += f"/{config.REDIS_DB or 0}"

        return redis_url

    def enqueue(
        self,
        func: Callable,
        *args,
        queue_name: str = DEFAULT_QUEUE,
        job_id: Optional[str] = None,
        job_timeout: int = 3600,
        **kwargs,
    ) -> Job:
        """
        Enqueue a function to be executed by a worker.

        Args:
            func: The function to execute
            *args: Arguments to pass to the function
            queue_name: Name of the queue to use
            job_id: Optional job ID (will be generated if not provided)
            job_timeout: Timeout for the job in seconds (default: 1 hour)
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Job: The created job
        """
        if not self.initialized:
            raise RuntimeError(
                "Queue manager not initialized. Call initialize() first."
            )

        if queue_name not in self.queues:
            raise ValueError(
                f"Queue '{queue_name}' not found. Available queues: {list(self.queues.keys())}"
            )

        queue = self.queues[queue_name]

        # Generate a job ID if not provided
        if job_id is None:
            job_id = str(uuid.uuid4())

        # Remove job-specific kwargs from function kwargs
        function_kwargs = dict(kwargs)
        function_kwargs.pop("job_timeout", None)
        function_kwargs.pop("queue_name", None)
        function_kwargs.pop("job_id", None)

        # Enqueue the job
        job = queue.enqueue(
            func, *args, **function_kwargs, job_id=job_id, timeout=job_timeout
        )

        logger.info(
            f"Job enqueued",
            props={
                "job_id": job.id,
                "queue": queue_name,
                "function": func.__name__,
                "timeout": job_timeout,
            },
        )

        return job

    def get_job(self, job_id: str) -> Optional[Job]:
        """
        Get a job by its ID.

        Args:
            job_id: The job ID

        Returns:
            Job or None: The job if found, None otherwise
        """
        if not self.initialized:
            raise RuntimeError(
                "Queue manager not initialized. Call initialize() first."
            )

        try:
            return Job.fetch(job_id, connection=self.redis_conn)
        except Exception as e:
            logger.error(f"Failed to fetch job {job_id}: {str(e)}")
            return None

    def get_queue_stats(self) -> Dict[str, Dict[str, int]]:
        """
        Get statistics for all queues.

        Returns:
            Dict: Statistics for each queue
        """
        if not self.initialized:
            raise RuntimeError(
                "Queue manager not initialized. Call initialize() first."
            )

        stats = {}
        for name, queue in self.queues.items():
            stats[name] = {
                "jobs": len(queue),
                "failed": len(queue.failed_job_registry),
                "scheduled": len(queue.scheduled_job_registry),
                "started": len(queue.started_job_registry),
                "deferred": len(queue.deferred_job_registry),
                "finished": len(queue.finished_job_registry),
            }

        return stats


# Singleton instance
_queue_manager = None


async def get_queue_manager() -> QueueManager:
    """
    Get the singleton queue manager instance.

    Returns:
        QueueManager: The initialized queue manager
    """
    global _queue_manager

    if _queue_manager is None:
        _queue_manager = QueueManager()
        await _queue_manager.initialize()

    return _queue_manager


async def cleanup_queue_manager():
    """Clean up the queue manager resources."""
    global _queue_manager

    if _queue_manager is not None and _queue_manager.redis_conn is not None:
        try:
            _queue_manager.redis_conn.close()
            logger.info("Queue manager resources cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up queue manager: {str(e)}")

        _queue_manager = None
