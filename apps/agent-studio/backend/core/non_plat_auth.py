import asyncio
import uuid
from typing import Any, Dict, <PERSON><PERSON>, <PERSON><PERSON>

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import API<PERSON>eyHeader

from backend.core.logging_config import get_logger
from backend.services.non_plat_auth_service import (
    NonPlatAuthService,
    get_non_plat_auth_service,
)

logger = get_logger("non_plat_auth_core")

# Define API key header schema
api_key_header = APIKeyHeader(name="x-anon-key", auto_error=False)


async def verify_api_key(
    api_key: str = Depends(api_key_header),
) -> Tuple[uuid.UUID, uuid.UUID, Optional[str], Optional[str]]:
    """Verify the API key using the NonPlatAuthService.

    Acts as a FastAPI dependency.

    Returns:
        Tuple containing (agent_id, org_id, hmac_secret_key_or_none, deployment_type_or_none)
    """
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is required",
        )

    # Get the service instance
    non_plat_auth_service: NonPlatAuthService = await get_non_plat_auth_service()
    # Call the service method - it now returns a dictionary
    deployment_data: Optional[
        Dict[str, Any]
    ] = await non_plat_auth_service.get_deployment_by_key(api_key)

    if not deployment_data:
        # Log the failure via the core logger if needed, though service likely logs it
        logger.warning(
            "Invalid API key provided.", props={"api_key_prefix": api_key[:5] + "..."}
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
        )

    try:
        agent_id = uuid.UUID(deployment_data["agent_id"])
        org_id = uuid.UUID(deployment_data["org_id"])
        # Extract the secret key using .get() as it might be None
        hmac_secret_key: Optional[str] = deployment_data.get("hmac_secret_key")
        # Extract deployment_type using .get()
        deployment_type: Optional[str] = deployment_data.get("deployment_type")

        logger.info(
            f"API key verified successfully for org {org_id}",
            props={
                "org_id": str(org_id),
                "agent_id": str(agent_id),
                "hmac_enabled": hmac_secret_key is not None,
            },
        )
        # Return the 4-element tuple
        return (agent_id, org_id, hmac_secret_key, deployment_type)
    except (KeyError, ValueError) as e:
        logger.exception(
            f"Error parsing deployment data from API key verification",
            props={"deployment_data": deployment_data, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing API key information",
        ) from e


async def non_plat_auth(
    request: Request,
    auth_data: Tuple[uuid.UUID, uuid.UUID, Optional[str], Optional[str]] = Depends(
        verify_api_key
    ),
) -> Tuple[uuid.UUID, uuid.UUID, dict, Optional[str], Optional[str]]:
    """
    Full non-platform authentication dependency.
    Uses verify_api_key and then gets/creates the user via NonPlatAuthService.
    Also performs HMAC verification on the request if headers and secret key are present.

    Returns:
        Tuple containing (agent_id, org_id, user_data, new_cookie, deployment_type)
        user_data is obtained via cookie/IP lookup (potentially unverified).
        new_cookie will be None if no new cookie was created
    """
    # Unpack the 4-element tuple from API key verification
    agent_id, org_id, hmac_secret_key, deployment_type = auth_data

    non_plat_auth_service: NonPlatAuthService = await get_non_plat_auth_service()

    try:
        # Get or create user based on cookie/IP via the service
        # This step fetches the *current* user based on cookie, regardless of HMAC status
        # It should happen *before* HMAC verification to identify the user
        user_data, new_cookie = await non_plat_auth_service.get_or_create_user(
            org_id=org_id, request=request
        )

        # --- Perform HMAC verification for the request --- #
        # We already have the hmac_secret_key from the verify_api_key dependency
        # If we didn't, we would need a service call here to fetch it based on org_id.
        _verify_hmac_for_request(request, hmac_secret_key)
        # If HMAC fails, the above function raises HTTPException(401)
        # --- End HMAC Verification --- #

        # --- Demotion Check: If user WAS verified but current request did NOT attempt/pass HMAC --- #
        hmac_attempted_on_this_request = bool(
            request.headers.get("x-user-email")
            and request.headers.get("x-user-name")
            and request.headers.get("x-user-hash")
            and hmac_secret_key
        )
        # Note: _verify_hmac_for_request already raised 401 if attempted and failed.
        # So, if we reach here and hmac_attempted_on_this_request is True, it must have passed.
        # We only need to unverify if it wasn't attempted AND the user is currently marked verified.
        if not hmac_attempted_on_this_request and user_data.get("verified"):
            logger.warning(
                f"User {user_data['id']} is marked verified in DB, but current request ({request.url.path}) is not HMAC verified. Unverifying.",
                props={
                    "user_id": user_data["id"],
                    "org_id": str(org_id),
                    "hmac_attempted_in_this_request": hmac_attempted_on_this_request,
                },
            )
            unverify_success = await non_plat_auth_service.unverify_user_session(
                uuid.UUID(user_data["id"])
            )
            if unverify_success:
                # Update local user_data to reflect the change for the current response
                user_data["verified"] = False
                user_data.pop("verified_user_email", None)
                user_data.pop("verified_user_name", None)
            else:
                logger.error(
                    f"Failed to unverify user {user_data['id']} in database during subsequent request demotion."
                )
                # Proceed with potentially stale 'verified' status, but log error
        # --- End Demotion Check --- #

        logger.info(
            f"Non-platform auth successful for user {user_data.get('id')}",
            props={
                "user_id": user_data.get("id"),
                "org_id": str(org_id),
                "agent_id": str(agent_id),
                "new_cookie_set": new_cookie is not None,
                "deployment_type": deployment_type,
            },
        )
        return (agent_id, org_id, user_data, new_cookie, deployment_type)

    except HTTPException:  # Re-raise HTTPExceptions from the service
        raise
    except Exception as e:
        # Catch unexpected errors during user get/create
        logger.exception(
            "Unexpected error during non_plat_auth user processing",
            props={
                "org_id": str(org_id),
                "agent_id": str(agent_id),
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to authenticate user due to an internal error.",
        ) from e


# --- Start: HMAC Verification Helper --- #
import hashlib
import hmac


def _verify_hmac_for_request(request: Request, hmac_secret_key: Optional[str]):
    """Internal helper to verify HMAC hash if headers and key are present."""

    x_user_email = request.headers.get("x-user-email")
    x_user_name = request.headers.get("x-user-name")
    x_user_hash = request.headers.get("x-user-hash")

    # Only proceed if all HMAC headers and the secret key are present
    if not (x_user_email and x_user_name and x_user_hash and hmac_secret_key):
        # If any required part is missing, HMAC is not being attempted or is incomplete.
        # We don't raise an error here, just return. Allows compatibility.
        logger.info(
            "Skipping HMAC verification: Headers or secret key missing.",
            props={
                "has_email": x_user_email is not None,
                "has_name": x_user_name is not None,
                "has_hash": x_user_hash is not None,
                "has_secret": hmac_secret_key is not None,
            },
        )
        return

    try:
        message_payload = f"{x_user_email}:{x_user_name}"
        message_bytes = message_payload.encode("utf-8")
        secret_bytes = hmac_secret_key.encode("utf-8")
        calculated_hash = hmac.new(
            secret_bytes, message_bytes, hashlib.sha256
        ).hexdigest()

        if not hmac.compare_digest(calculated_hash, x_user_hash):
            logger.warning(
                "HMAC verification failed for subsequent request.",
                props={
                    "api_provided_email": x_user_email,
                    "api_provided_name": x_user_name,
                    "api_provided_hash_prefix": x_user_hash[:5] + "...",
                    "calculated_hash_prefix": calculated_hash[:5] + "...",
                },
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid HMAC hash"
            )
        else:
            # Log success only if needed for debugging, might be verbose
            # Log success following specified format, including hash prefix for verification
            logger.info(
                "HMAC verification successful for subsequent request. Email: %s, Name: %s, Hash Prefix: %s",
                x_user_email,
                x_user_name,
                x_user_hash[:5] + "..." if x_user_hash else "None",  # Use prefix
                props={
                    "verified_email": x_user_email,
                    "verified_name": x_user_name,
                    "verified_hash_prefix": (
                        x_user_hash[:5] + "..." if x_user_hash else "None"
                    ),  # Use prefix
                },
            )

    except Exception as e:
        logger.exception(
            "Error during subsequent HMAC verification process.",
            props={
                "api_provided_email": x_user_email,
                "error": str(e),
            },
        )
        # Re-raise as a 500 error because something went wrong during verification itself
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during HMAC verification.",
        ) from e


# --- End: HMAC Verification Helper --- #


# Note: The cleanup function `cleanup_non_plat_auth_service` from the service file
# should be called during application shutdown (e.g., in main.py's shutdown event)
# alongside other service cleanup functions.
