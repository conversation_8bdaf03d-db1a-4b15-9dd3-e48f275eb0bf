import multiprocessing

multiprocessing.set_start_method("spawn", force=True)

"""
RQ Worker script for processing background tasks.
Run this script in a separate process to handle background tasks.
"""

import logging
import os

# Import config and services
import sys

import redis
from rq import Queue, Worker

# When running as a module
try:
    from backend.core.config import get_config
    from backend.services.agent_file_service import AgentFileService
    from backend.services.flow_service import FlowService
except ImportError:
    # When running directly
    try:
        from core.config import get_config
        from services.agent_file_service import AgentFileService
        from services.flow_service import FlowService
    except ImportError:
        # Last resort - add backend to path
        backend_dir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
        if backend_dir not in sys.path:
            sys.path.insert(0, backend_dir)
        from core.config import get_config
        from services.agent_file_service import AgentFileService
        from services.flow_service import FlowService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger("rq_worker")


def get_redis_connection():
    """Get Redis connection for RQ worker using config module."""
    config = get_config()
    redis_host = config.REDIS_HOST
    redis_port = config.REDIS_PORT
    redis_db_raw = getattr(config, "REDIS_DB", "0")
    try:
        # Handle 'default' as a special case
        if redis_db_raw == "default":
            redis_db = 0
            logger.info("Using default Redis DB (0)")
        else:
            redis_db = int(redis_db_raw)
    except ValueError:
        logger.warning(
            f"REDIS_DB config value is not a valid integer ('{redis_db_raw}'). Defaulting to 0."
        )
        redis_db = 0
    redis_password = config.REDIS_PASSWORD
    return redis.Redis(
        host=redis_host, port=redis_port, db=redis_db, password=redis_password
    )


if __name__ == "__main__":
    queue_name = sys.argv[1] if len(sys.argv) > 1 else "default"
    redis_conn = get_redis_connection()
    worker = Worker([queue_name], connection=redis_conn)
    logger.info(f"Starting RQ worker for queue '{queue_name}'")
    worker.work()
