import asyncio
import time
import traceback
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional

from redis.asyncio.client import Redis
from redis.asyncio.connection import ConnectionPool
from redis.exceptions import ConnectionError

from backend.core.logging_config import get_logger

from .config import get_config

# Use the application's logger for consistent formatting
logger = get_logger("redis-manager")


class RedisConnectionManager:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "initialized"):
            config = get_config()
            self.pool = None
            self.max_connections = (
                config.REDIS_CONNECTION_POOL_SIZE
            )  # Use environment value instead of hardcoded 100
            self.current_connections = 0
            self.connection_timestamps = {}  # Track when connections were created
            self.connection_owners = {}  # Track which component owns each connection
            # Pre-initialized connection management
            self.pre_initialized_connections = []
            self.available_pre_initialized = (
                []
            )  # Pre-initialized connections available for use
            self.in_use_pre_initialized = {}  # Map of conn_id to owner
            self.pre_init_lock = (
                asyncio.Lock()
            )  # Lock for accessing pre-initialized connections
            self.stats_logger_task = None
            self.health_check_task = None
            self.initialized = True

    async def initialize(self, redis_url: str):
        """
        Initialize the Redis connection pool.

        Args:
            redis_url: The Redis connection URL
        """
        if self.pool is None:
            # Redact password from URL for logging
            redacted_url = redis_url.replace(redis_url.split("@")[0], "redis://****")
            logger.info(
                "Initializing Redis connection pool",
                props={
                    "url_format": redacted_url,
                    "max_connections": self.max_connections,
                },
            )
            try:
                try:
                    # Create the connection pool - optimized for high-latency environments
                    # Note: We don't use socket_keepalive as it may add overhead
                    self.pool = ConnectionPool.from_url(
                        redis_url,
                        max_connections=self.max_connections,
                        decode_responses=True,
                        socket_timeout=10.0,  # Timeout for operations
                        socket_connect_timeout=5.0,  # Timeout for initial connection
                        retry_on_timeout=True,  # Auto retry on timeout
                    )

                    # Test the connection
                    test_conn = Redis(connection_pool=self.pool)

                    ping_result = await test_conn.ping()
                    if not ping_result:
                        raise Exception("Redis ping failed")

                    # Get server info to verify connection details
                    info = await test_conn.info()
                    connected_clients = info.get("clients", {}).get(
                        "connected_clients", 0
                    )

                    logger.info(
                        "Redis server connection details",
                        props={
                            "connected_clients": connected_clients,
                            "redis_version": info.get("server", {}).get(
                                "redis_version", "unknown"
                            ),
                        },
                    )

                except Exception as e:
                    logger.error(
                        "Redis connection test failed",
                        props={"error": str(e), "error_type": type(e).__name__},
                    )
                    if self.pool:
                        await self.pool.aclose()
                        self.pool = None
                    raise
                finally:
                    # Properly close the test connection
                    if "test_conn" in locals() and test_conn:
                        await test_conn.aclose()

                logger.info("Redis connection pool created successfully")

                # Start connection stats logger as an async task
                if self.stats_logger_task is None:
                    self.stats_logger_task = asyncio.create_task(
                        self._log_connection_stats()
                    )
                    # Make sure the task is properly handled
                    self.stats_logger_task.add_done_callback(
                        lambda t: self._handle_stats_logger_completion(t)
                    )

                logger.info(
                    "Redis connection manager initialized",
                    props={
                        "max_connections": self.max_connections,
                        "current_connections": self.current_connections,
                    },
                )
            except Exception as e:
                logger.error(
                    "Failed to create Redis connection pool",
                    props={"error": str(e), "error_type": type(e).__name__},
                )
                # Clean up if initialization fails
                if self.pool:
                    try:
                        await self.pool.aclose()
                    except Exception:
                        pass
                    self.pool = None
                raise

    def _handle_stats_logger_completion(self, task):
        """Handle completion of the stats logger task, including cancellation."""
        try:
            # Get the exception if there is one
            exc = task.exception()
            # Only log non-cancellation exceptions
            if exc is not None and not isinstance(exc, asyncio.CancelledError):
                logger.error(
                    "Stats logger task ended with error",
                    props={"error": str(exc), "error_type": type(exc).__name__},
                )
        except asyncio.CancelledError:
            # Task was cancelled - this is normal during shutdown
            pass
        except Exception as e:
            logger.error(f"Error handling stats logger completion: {e}")

    async def pre_initialize_connections(self, count=20):
        """Pre-initialize a set number of Redis connections for faster access.

        Args:
            count: Number of connections to pre-initialize

        Returns:
            int: Number of successfully initialized connections
        """
        if not self.pool:
            logger.error(
                "Cannot pre-initialize connections: Redis pool not initialized"
            )
            return 0

        logger.info(f"Pre-initializing {count} Redis connections")
        successful = 0

        async with self.pre_init_lock:
            # Clear existing pre-initialized connections if any
            await self._cleanup_pre_initialized_connections()

            # Create new connections
            for i in range(count):
                try:
                    # Create connection
                    conn = Redis.from_pool(self.pool)

                    # Validate with ping
                    try:
                        await conn.ping()
                    except Exception as e:
                        logger.warning(
                            f"Failed to validate pre-initialized connection {i+1}/{count}: {str(e)}"
                        )
                        await conn.aclose()
                        continue

                    # Store connection
                    conn_id = id(conn)
                    self.pre_initialized_connections.append(conn)
                    self.available_pre_initialized.append(conn)

                    # Track as a system connection but not in the regular counter
                    self.connection_timestamps[conn_id] = time.time()
                    self.connection_owners[conn_id] = "pre_initialized"

                    successful += 1
                    logger.debug(f"Pre-initialized connection {successful}/{count}")

                except Exception as e:
                    logger.error(
                        f"Error pre-initializing Redis connection {i+1}/{count}: {str(e)}"
                    )

            logger.info(
                f"Successfully pre-initialized {successful}/{count} Redis connections"
            )

            # Start background health check task if not running
            if self.health_check_task is None or self.health_check_task.done():
                self.health_check_task = asyncio.create_task(
                    self._background_health_check()
                )
                # Make sure the task is properly handled
                self.health_check_task.add_done_callback(
                    lambda t: self._handle_task_completion(t, "health_check")
                )
                logger.info(
                    "Started background health check task for pre-initialized connections"
                )

            return successful

    async def _cleanup_pre_initialized_connections(self):
        """Clean up all pre-initialized connections."""
        cleanup_count = 0

        # Close all pre-initialized connections
        for conn in self.pre_initialized_connections:
            try:
                conn_id = id(conn)
                # Remove from tracking dictionaries
                self.connection_timestamps.pop(conn_id, None)
                self.connection_owners.pop(conn_id, None)

                # Close the connection
                await conn.aclose()
                cleanup_count += 1
            except Exception as e:
                logger.error(f"Error closing pre-initialized connection: {str(e)}")

        # Clear the lists
        self.pre_initialized_connections = []
        self.available_pre_initialized = []
        self.in_use_pre_initialized = {}

        logger.info(f"Cleaned up {cleanup_count} pre-initialized Redis connections")

    async def get_connection(
        self, owner: str = "unknown", db: Optional[str] = None
    ) -> Redis:
        """Get a Redis connection with proper tracking.

        First tries to use a pre-initialized connection if available,
        otherwise falls back to getting a connection from the pool.

        Args:
            owner: Identifier for who's requesting the connection

        Returns:
            Redis: An active Redis connection
        """
        if not self.pool:
            logger.error("Cannot get connection: Redis pool not initialized")
            raise ConnectionError("Redis pool not initialized")

        # First try to get a pre-initialized connection
        async with self.pre_init_lock:
            if self.available_pre_initialized:
                conn = self.available_pre_initialized.pop(0)
                # Trust pre-initialized connections without re-verification
                # (they were already verified during initialization)
                conn_id = id(conn)
                self.in_use_pre_initialized[conn_id] = owner
                # Update the timestamp for connection freshness tracking
                self.connection_timestamps[conn_id] = time.time()
                self.connection_owners[conn_id] = owner
                logger.info(
                    f"Redis pre-initialized connection obtained by {owner} "
                    f"(available: {len(self.available_pre_initialized)})"
                )
                return conn

        # Fall back to regular connection pool if no pre-initialized connections available
        if self.current_connections >= self.max_connections:
            logger.warning(
                "Max Redis connections reached. Waiting for available connection...",
                props={
                    "current_connections": self.current_connections,
                    "max_connections": self.max_connections,
                    "owner": owner,
                },
            )
            wait_start = time.time()
            while self.current_connections >= self.max_connections:
                await asyncio.sleep(0.1)
                # Timeout after 10 seconds of waiting
                if time.time() - wait_start > 10:
                    logger.error(
                        "Timed out waiting for available Redis connection",
                        props={
                            "wait_time_seconds": time.time() - wait_start,
                            "current_connections": self.current_connections,
                            "owner": owner,
                        },
                    )
                    # Force close stale connections if we hit the timeout
                    closed = await self.force_close_stale_connections(
                        max_age_seconds=60
                    )
                    if closed:
                        logger.info(
                            "Force closed stale connections",
                            props={"closed_count": len(closed), "owner": owner},
                        )
                    else:
                        raise ConnectionError(
                            "Timed out waiting for available Redis connection"
                        )

        # Get a connection from the pool without verification
        try:
            # Use from_pool method as recommended in the documentation
            conn = Redis.from_pool(self.pool)

            # Track the connection
            conn_id = id(conn)
            self.current_connections += 1
            self.connection_timestamps[conn_id] = time.time()
            self.connection_owners[conn_id] = owner

            logger.debug(
                f"Redis connection obtained by {owner} (current: {self.current_connections})"
            )
            return conn

        except Exception as e:
            logger.error(f"Error getting Redis connection: {str(e)}")
            raise ConnectionError(f"Failed to get Redis connection: {str(e)}")

    @asynccontextmanager
    async def connection(self, owner: str = "unknown"):
        """Context manager for Redis connections that automatically handles closing.

        Args:
            owner: Identifier for who's requesting the connection

        Example usage:
        async with redis_manager.connection("MyService") as conn:
            await conn.set("key", "value")
        """
        conn = None
        try:
            conn = await self.get_connection(owner)
            yield conn
        finally:
            if conn:
                await self.release_connection(conn)

    async def release_connection(self, conn: Redis):
        """Properly close and release a Redis connection.

        If the connection is pre-initialized, it will be returned to the available pool.
        Otherwise, it will be closed and returned to the connection pool.
        """
        if not conn:
            logger.warning("Attempted to release None connection")
            return

        try:
            conn_id = id(conn)
            owner = self.connection_owners.get(conn_id, "unknown")

            # Check if this is a pre-initialized connection
            async with self.pre_init_lock:
                if conn in self.pre_initialized_connections:
                    # Return to available pool WITHOUT health verification
                    # Skip the health check to avoid unnecessary latency

                    # Return to available pool
                    if conn_id in self.in_use_pre_initialized:
                        self.in_use_pre_initialized.pop(conn_id, None)

                    if conn not in self.available_pre_initialized:
                        self.available_pre_initialized.append(conn)

                    # Update timestamp
                    self.connection_timestamps[conn_id] = time.time()
                    logger.info(
                        f"Redis pre-initialized connection returned by {owner} "
                        f"(available: {len(self.available_pre_initialized)})"
                    )
                    return

            # Regular connection close logic for non-pre-initialized connections
            # Check if this connection is actually tracked
            if conn_id not in self.connection_timestamps:
                logger.warning(
                    f"Attempted to release untracked connection (owner: {owner})"
                )
                # Still try to close it
                try:
                    await conn.aclose()
                except Exception as e:
                    logger.error(f"Error closing untracked connection: {str(e)}")
                return

            # Get connection age for logging
            conn_age = time.time() - self.connection_timestamps.get(
                conn_id, time.time()
            )

            # Use aclose() as recommended in the documentation
            try:
                await conn.aclose()
                logger.info(
                    f"Redis connection closed successfully (age: {conn_age:.1f}s)"
                )
            except Exception as e:
                logger.error(f"Error closing Redis connection: {str(e)}")
                # Continue with cleanup even if close fails

            # Update tracking counters
            self.current_connections = max(0, self.current_connections - 1)

            # Clean up tracking dictionaries
            self.connection_timestamps.pop(conn_id, None)
            self.connection_owners.pop(conn_id, None)

            # Log connection release
            logger.info(
                f"Redis connection released by {owner} (current: {self.current_connections}/{self.max_connections})"
            )
        except Exception as e:
            logger.error(f"Error in release_connection: {str(e)}")
            # Try to decrement the counter even if an error occurred
            self.current_connections = max(0, self.current_connections - 1)

    async def check_connection_health(self, conn: Redis) -> bool:
        """Check if a connection is still healthy."""
        try:
            await conn.ping()
            return True
        except Exception as e:
            logger.warning(f"Connection health check failed: {str(e)}")
            return False

    async def close_pool(self):
        """Close the Redis connection pool and all connections."""
        if not self.pool:
            logger.info("No Redis pool to close")
            return

        logger.info("Closing Redis connection pool and releasing all connections")

        # Cancel background tasks
        if self.health_check_task and not self.health_check_task.done():
            logger.info("Cancelling background health check task")
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logger.error(f"Error cancelling health check task: {e}")

        if self.stats_logger_task and not self.stats_logger_task.done():
            logger.info("Cancelling stats logger task")
            self.stats_logger_task.cancel()
            try:
                await self.stats_logger_task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logger.error(f"Error cancelling stats logger task: {e}")

        # First, clean up pre-initialized connections
        await self._cleanup_pre_initialized_connections()

        if self.pool:
            logger.info("Closing Redis connection pool...")

            # Close all tracked connections first
            conn_ids = list(self.connection_owners.keys())
            logger.info(f"Closing {len(conn_ids)} tracked Redis connections")

            # Track futures for all connection close operations
            close_futures = []
            for conn_id in conn_ids:
                try:
                    # Find the connection object by id
                    for conn in self._get_all_connections():
                        if id(conn) == conn_id:
                            owner = self.connection_owners.get(conn_id, "unknown")
                            logger.info(f"Closing connection owned by {owner}")
                            # Add to futures list instead of awaiting immediately
                            close_futures.append(self.release_connection(conn))
                            break
                except Exception as e:
                    logger.error(f"Error closing connection {conn_id}: {str(e)}")

            # Wait for all connection close operations to complete
            if close_futures:
                try:
                    # Use gather with return_exceptions to prevent one failure from halting all
                    await asyncio.gather(*close_futures, return_exceptions=True)
                    logger.info(
                        f"Completed closing {len(close_futures)} Redis connections"
                    )
                except Exception as e:
                    logger.error(
                        f"Error waiting for connection close operations: {str(e)}"
                    )

            # Close the pool
            try:
                # Get connection stats before closing
                try:
                    stats = await self.get_connection_stats()
                    logger.info(
                        f"Final connection stats before pool close: {stats['current_connections']}/{stats['max_connections']} connections"
                    )
                except Exception as e:
                    logger.error(f"Error getting final connection stats: {str(e)}")

                # Add a small delay to ensure all connections have been processed
                await asyncio.sleep(0.5)

                # Close the pool
                try:
                    pool_close_timeout = 5.0  # 5 seconds timeout
                    close_task = asyncio.create_task(self.pool.aclose())
                    await asyncio.wait_for(close_task, timeout=pool_close_timeout)
                    logger.info("Redis connection pool closed successfully")
                except asyncio.TimeoutError:
                    logger.warning(
                        f"Redis pool close operation timed out after {pool_close_timeout}s"
                    )
                except Exception as e:
                    logger.error(f"Error in pool.aclose(): {str(e)}")
            except Exception as e:
                logger.error(f"Error closing Redis connection pool: {str(e)}")

            # Reset state
            self.pool = None
            self.current_connections = 0
            self.connection_timestamps.clear()
            self.connection_owners.clear()

            logger.info("Redis connection manager reset to initial state")

    def _get_all_connections(self):
        """Get all connections from the pool (for cleanup purposes)."""
        if not self.pool:
            return []

        # This is a best effort approach to get connections from the pool
        # The actual implementation depends on the internal structure of the pool
        connections = []
        if hasattr(self.pool, "_in_use_connections"):
            connections.extend(self.pool._in_use_connections)
        if hasattr(self.pool, "_available_connections"):
            connections.extend(self.pool._available_connections)
        return connections

    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        active_connections = len(self.connection_timestamps)
        oldest_connection_age = None
        oldest_connection_owner = None
        connection_ages = []
        current_time = time.time()

        if self.connection_timestamps:
            oldest_conn_id = min(
                self.connection_timestamps.items(), key=lambda x: x[1]
            )[0]
            oldest_time = self.connection_timestamps[oldest_conn_id]
            oldest_connection_age = current_time - oldest_time
            oldest_connection_owner = self.connection_owners.get(
                oldest_conn_id, "unknown"
            )
            connection_ages = [
                current_time - t for t in self.connection_timestamps.values()
            ]

        # Get connection owners distribution
        owner_counts = {}
        for owner in self.connection_owners.values():
            owner_counts[owner] = owner_counts.get(owner, 0) + 1

        # Get actual Redis connection count if possible
        actual_connections = await self.get_actual_redis_connections()

        # Add pre-initialized connection stats
        stats = {
            "current_connections": self.current_connections,
            "max_connections": self.max_connections,
            "active_connections": active_connections,
            "actual_redis_connections": actual_connections,
            "oldest_connection_age_seconds": oldest_connection_age,
            "oldest_connection_owner": oldest_connection_owner,
            "connection_usage_percent": (
                (self.current_connections / self.max_connections) * 100
                if self.max_connections > 0
                else 0
            ),
            "timestamp": datetime.utcnow().isoformat(),
            "connection_owners": owner_counts,
            "connection_count_by_age": {
                "0_30s": len([age for age in connection_ages if age <= 30]),
                "30s_2min": len([age for age in connection_ages if 30 < age <= 120]),
                "2min_5min": len([age for age in connection_ages if 120 < age <= 300]),
                "5min_plus": len([age for age in connection_ages if age > 300]),
            },
            "pre_initialized_total": len(self.pre_initialized_connections),
            "pre_initialized_available": len(self.available_pre_initialized),
            "pre_initialized_in_use": len(self.in_use_pre_initialized),
        }

        return stats

    async def get_actual_redis_connections(self) -> Optional[int]:
        """Get the actual number of connections directly from Redis server using CLIENT LIST."""
        if not self.pool:
            logger.warning("Cannot get actual Redis connections: pool not initialized")
            return None

        try:
            # Create a temporary connection for this operation using context manager
            async with self.connection("ConnectionStats") as temp_conn:
                # Execute CLIENT LIST command
                logger.debug("Executing CLIENT LIST command")
                client_list = await temp_conn.execute_command("CLIENT LIST")

                # Log the response type for debugging
                logger.debug(f"CLIENT LIST response type: {type(client_list)}")

                # Handle different response types
                connections = []

                # If it's bytes, decode it
                if isinstance(client_list, bytes):
                    logger.debug("Decoding bytes response")
                    client_list = client_list.decode("utf-8")

                # If it's a string, split it into lines
                if isinstance(client_list, str):
                    logger.debug("Processing string response")
                    connections = client_list.strip().split("\n")
                # If it's already a list, use it directly
                elif isinstance(client_list, list):
                    logger.debug("Processing list response")
                    connections = client_list
                    # If items are bytes, decode them
                    if connections and isinstance(connections[0], bytes):
                        logger.debug("Decoding bytes items in list")
                        connections = [conn.decode("utf-8") for conn in connections]
                else:
                    logger.warning(
                        f"Unexpected CLIENT LIST response type: {type(client_list)}"
                    )
                    return None

                connection_count = len(connections)

                # Log detailed client information at debug level
                logger.debug(
                    f"Redis CLIENT LIST returned {connection_count} connections"
                )
                for i, client in enumerate(connections):
                    logger.debug(f"Redis client {i+1}: {client}")

                return connection_count
        except Exception as e:
            logger.error(f"Error getting actual Redis connection count: {str(e)}")
            # Log the full exception traceback at debug level
            logger.debug(
                f"Redis connection count error traceback: {traceback.format_exc()}"
            )
            return None

    async def _log_connection_stats(self):
        """Background task that periodically logs connection statistics."""
        try:
            # Wait a bit before starting to make sure app is fully initialized
            await asyncio.sleep(60)

            # Log initial stats
            await self.log_connection_stats()

            # Then enter regular logging cycle
            while True:
                try:
                    stats = await self.get_connection_stats()

                    # Log connection stats with actual Redis connections if available
                    if stats["actual_redis_connections"] is not None:
                        logger.info(
                            f"Redis connections: {stats['current_connections']}/{stats['max_connections']} "
                            + f"({stats['connection_usage_percent']:.1f}% used) | "
                            + f"Actual Redis connections: {stats['actual_redis_connections']}"
                        )
                    else:
                        logger.info(
                            f"Redis connections: {stats['current_connections']}/{stats['max_connections']} "
                            + f"({stats['connection_usage_percent']:.1f}% used)"
                        )

                    # Log warnings if approaching limits
                    if (
                        stats["current_connections"] >= self.max_connections * 0.8
                    ):  # 80% of max
                        logger.warning(
                            f"Redis connection count approaching limit: {stats['current_connections']}/{self.max_connections}"
                        )

                    # Log warnings for old connections - increased from 5 minutes to 30 minutes
                    if (
                        stats["oldest_connection_age_seconds"]
                        and stats["oldest_connection_age_seconds"] > 1800
                    ):  # 30 minutes
                        logger.warning(
                            f"Redis connection age exceeds threshold: {stats['oldest_connection_age_seconds']:.1f}s "
                            + f"by {stats['oldest_connection_owner']}"
                        )

                    await asyncio.sleep(
                        300
                    )  # Log every 5 minutes instead of every minute
                except Exception as e:
                    logger.error(f"Error in connection stats logger: {str(e)}")
                    await asyncio.sleep(300)  # Also wait 5 minutes on error
        except asyncio.CancelledError:
            # This is expected during shutdown
            logger.info("Connection stats logger task cancelled")
        except Exception as e:
            logger.error(f"Connection stats logger task encountered an error: {str(e)}")

    async def log_connection_stats(self):
        """Manually trigger connection stats logging."""
        try:
            stats = await self.get_connection_stats()
            if stats["actual_redis_connections"] is not None:
                logger.info(
                    "Redis connections",
                    props={
                        "current_connections": stats["current_connections"],
                        "max_connections": stats["max_connections"],
                        "connection_usage_percent": stats["connection_usage_percent"],
                        "actual_redis_connections": stats["actual_redis_connections"],
                    },
                )
            else:
                logger.info(
                    "Redis connections",
                    props={
                        "current_connections": stats["current_connections"],
                        "max_connections": stats["max_connections"],
                        "connection_usage_percent": stats["connection_usage_percent"],
                    },
                )
            return stats
        except Exception as e:
            logger.error(
                "Error generating connection stats",
                props={"error": str(e), "error_type": type(e).__name__},
            )
            raise

    async def force_close_stale_connections(
        self, max_age_seconds: int = 300
    ) -> List[Dict[str, Any]]:
        """Force close connections that are older than the specified age."""
        closed_connections = []
        current_time = time.time()
        stale_conn_ids = []

        # Find stale connections
        for conn_id, timestamp in self.connection_timestamps.items():
            age = current_time - timestamp
            if age > max_age_seconds:
                stale_conn_ids.append(conn_id)
                owner = self.connection_owners.get(conn_id, "unknown")
                logger.warning(
                    "Found stale connection",
                    props={"conn_id": conn_id, "owner": owner, "age_seconds": age},
                )

        # Close stale connections
        for conn_id in stale_conn_ids:
            try:
                owner = self.connection_owners.get(conn_id, "unknown")
                # Find the connection object by id
                for conn in self._get_all_connections():
                    if id(conn) == conn_id:
                        logger.warning(
                            "Forcibly closing stale connection",
                            props={"conn_id": conn_id, "owner": owner},
                        )
                        await self.release_connection(conn)
                        closed_connections.append(
                            {
                                "conn_id": conn_id,
                                "owner": owner,
                                "age_seconds": current_time
                                - self.connection_timestamps.get(conn_id, current_time),
                            }
                        )
                        break
            except Exception as e:
                logger.error(
                    "Error closing stale connection",
                    props={
                        "conn_id": conn_id,
                        "error": str(e),
                        "error_type": type(e).__name__,
                    },
                )

        return closed_connections

    async def create_pipeline(self, conn: Redis, transaction: bool = True):
        """Create a Redis pipeline for executing multiple commands atomically.

        Example usage:
        async with redis_manager.connection("MyService") as conn:
            async with redis_manager.create_pipeline(conn) as pipe:
                await pipe.set("key1", "value1").set("key2", "value2").execute()
        """
        return conn.pipeline(transaction=transaction)

    async def _background_health_check(self):
        """Background task to periodically check health of pre-initialized connections.

        This runs every 5 minutes to verify pre-initialized connections are still healthy,
        replacing any that are not responding.
        """
        try:
            # Wait a bit before starting to make sure app is fully initialized
            await asyncio.sleep(120)  # Start after 2 minutes

            while True:
                await asyncio.sleep(300)  # Check every 5 minutes, was 60 seconds

                if not self.pre_initialized_connections:
                    logger.debug("No pre-initialized connections to check")
                    continue

                logger.debug(
                    f"Performing health check on {len(self.pre_initialized_connections)} pre-initialized connections"
                )

                # Only check connections that are available (not in use)
                async with self.pre_init_lock:
                    # Count available connections
                    available_count = len(self.available_pre_initialized)

                    # Skip check if all connections are in use
                    if available_count == 0:
                        logger.debug(
                            "All pre-initialized connections are in use, skipping health check"
                        )
                        continue

                    # Only check a sample of connections to reduce overhead
                    check_count = min(
                        3, available_count
                    )  # Check at most 3 connections at a time
                    check_connections = self.available_pre_initialized[:check_count]

                    unhealthy_connections = []

                    # Check a sample of available connections
                    for conn in check_connections:
                        try:
                            # Quick ping to verify connection
                            is_healthy = await self.check_connection_health(conn)
                            if not is_healthy:
                                unhealthy_connections.append(conn)
                        except Exception as e:
                            logger.warning(
                                f"Health check failed for pre-initialized connection: {e}"
                            )
                            unhealthy_connections.append(conn)

                    # Remove and replace unhealthy connections
                    if unhealthy_connections:
                        logger.warning(
                            f"Found {len(unhealthy_connections)} unhealthy pre-initialized connections to replace"
                        )

                        for conn in unhealthy_connections:
                            try:
                                # Remove from tracking lists
                                if conn in self.pre_initialized_connections:
                                    self.pre_initialized_connections.remove(conn)
                                if conn in self.available_pre_initialized:
                                    self.available_pre_initialized.remove(conn)

                                # Close the connection
                                conn_id = id(conn)
                                self.connection_timestamps.pop(conn_id, None)
                                self.connection_owners.pop(conn_id, None)
                                await conn.aclose()

                                # Create a replacement connection
                                try:
                                    new_conn = Redis.from_pool(self.pool)
                                    await new_conn.ping()  # Verify the new connection

                                    # Add to tracking lists
                                    new_conn_id = id(new_conn)
                                    self.pre_initialized_connections.append(new_conn)
                                    self.available_pre_initialized.append(new_conn)
                                    self.connection_timestamps[
                                        new_conn_id
                                    ] = time.time()
                                    self.connection_owners[
                                        new_conn_id
                                    ] = "pre_initialized"

                                    logger.info(
                                        "Successfully replaced unhealthy pre-initialized connection"
                                    )
                                except Exception as e:
                                    logger.error(
                                        f"Failed to create replacement connection: {e}"
                                    )
                            except Exception as e:
                                logger.error(
                                    f"Error handling unhealthy connection: {e}"
                                )
                    else:
                        logger.debug(
                            "All checked pre-initialized connections are healthy"
                        )

        except asyncio.CancelledError:
            logger.info("Background health check task cancelled")
        except Exception as e:
            logger.error(f"Background health check task encountered an error: {e}")

    def _handle_task_completion(self, task, task_name):
        """Generic handler for background task completion."""
        try:
            # Get the exception if there is one
            exc = task.exception()
            # Only log non-cancellation exceptions
            if exc is not None and not isinstance(exc, asyncio.CancelledError):
                logger.error(f"{task_name} task ended with error: {exc}")
        except asyncio.CancelledError:
            # Task was cancelled - this is normal during shutdown
            pass
        except Exception as e:
            logger.error(f"Error handling {task_name} task completion: {e}")
