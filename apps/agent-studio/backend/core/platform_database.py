"""Platform database configuration and utilities."""

import asyncio
import time
from typing import Optional

from supabase._async.client import AsyncClient as Client
from supabase._async.client import create_client

from ..utils.logger import get_logger
from .config import get_config

logger = get_logger("platform_database")
settings = get_config()

# Global state for platform database connection
_platform_supabase_client: Optional[Client] = None
_platform_initialization_lock = asyncio.Lock()
_platform_initialized = False
_platform_last_health_check = 0
_platform_health_check_interval = 300  # 5 minutes


async def get_platform_supabase(auth_token: Optional[str] = None) -> Client:
    """
    Get the Supabase client for Platform, initializing it if necessary.
    This function ensures the client is initialized before returning it.

    Args:
        auth_token: Optional bearer token from API request for authentication

    Returns:
        Client: The initialized Supabase client for Platform

    Raises:
        RuntimeError: If initialization fails after retries
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        start_time = time.time()
        try:
            global _platform_supabase_client, _platform_initialized
            if not _platform_initialized or auth_token:
                # Always reinitialize if auth_token is provided to ensure we use the latest token
                init_start = time.time()
                await initialize_platform_supabase(auth_token)
                init_time = time.time() - init_start
                logger.debug(
                    "Platform Supabase initialization took %.4f seconds", init_time, props={}
                )
            await check_platform_supabase_health_if_needed()
            total_time = time.time() - start_time
            if total_time > 0.1:
                logger.warning(
                    "Platform Supabase client acquisition took %.4f seconds", total_time, props={}
                )
            return _platform_supabase_client
        except Exception as e:
            retry_count += 1
            error_time = time.time() - start_time
            logger.error(
                "Error getting Platform Supabase client after %.4f seconds: %s (attempt %d/%d)",
                error_time, e, retry_count, max_retries, props={},
            )
            if ("connection" in str(e).lower() or "timeout" in str(e).lower()) and retry_count < max_retries:
                logger.warning(
                    "Detected potential connection issue, attempting to reinitialize Platform Supabase client",
                    props={},
                )
                _platform_initialized = False
                # Wait before retrying, with increasing backoff
                await asyncio.sleep(1 * retry_count)
                continue
            # If we've reached max retries or it's not a connection issue, raise the exception
            raise


async def initialize_platform_supabase(auth_token: Optional[str] = None) -> None:
    """
    Initialize the Supabase client for Platform asynchronously with retry logic.
    This function is called once to initialize the client.
    
    Args:
        auth_token: Optional bearer token from API request for authentication
    """
    global _platform_supabase_client, _platform_initialized

    # Use a lock to prevent multiple initializations
    async with _platform_initialization_lock:
        # If auth_token is provided, we always reinitialize to use the latest token
        if _platform_initialized and not auth_token:
            return

        # Get Platform Supabase credentials from settings
        platform_supabase_url = settings.PLATFORM_SUPABASE_URL
        platform_supabase_key = settings.PLATFORM_SUPABASE_KEY
        
        if not platform_supabase_url or not platform_supabase_key:
            logger.error("Platform Supabase credentials not found in settings", props={})
            raise RuntimeError("Platform Supabase credentials not found in settings")
        
        logger.info("Initializing Platform Supabase client with URL: %s", platform_supabase_url, props={})
        
        # Retry logic for platform Supabase initialization
        max_retries = 3
        retry_count = 0
        last_error = None

        while retry_count < max_retries:
            try:
                logger.info(
                    "Platform Supabase initialization attempt %d/%d",
                    retry_count + 1, max_retries, props={},
                )
                
                # Create the client with the anon key
                _platform_supabase_client = await create_client(
                    platform_supabase_url, 
                    platform_supabase_key
                )
                
                # If auth token is provided, set it for the postgrest client
                if auth_token:
                    # This is the recommended approach based on:
                    # https://github.com/supabase/supabase-py/issues/420#issuecomment-**********
                    logger.info("Setting auth token for Platform Supabase client", props={})
                    
                    # Set the auth token for the postgrest client
                    _platform_supabase_client.postgrest.auth(auth_token)
                
                # Mark as initialized without checking health
                _platform_initialized = True
                logger.info("Platform Supabase client initialized successfully", props={})
                return
            except Exception as e:
                retry_count += 1
                last_error = e
                logger.warning(
                    "Platform Supabase initialization attempt %d failed: %s",
                    retry_count, e, props={},
                )
                if retry_count < max_retries:
                    await asyncio.sleep(1 * retry_count)
        
        logger.error(
            "Failed to initialize Platform Supabase after %d attempts: %s",
            max_retries, last_error, props={},
        )
        raise RuntimeError("Platform Supabase initialization failed: %s" % last_error)


async def check_platform_supabase_health_if_needed() -> bool:
    """
    Check Platform Supabase health if it's been too long since the last check.
    
    Returns:
        bool: True if the health check passed, False otherwise
    """
    global _platform_last_health_check
    current_time = time.time()
    
    if current_time - _platform_last_health_check > _platform_health_check_interval:
        return await check_platform_supabase_health()
    
    return True


async def check_platform_supabase_health() -> bool:
    """
    Check if the Platform Supabase connection is healthy by checking if the client is initialized.
    
    Returns:
        bool: True if the connection is healthy, False otherwise
    """
    global _platform_last_health_check
    _platform_last_health_check = time.time()
    
    try:
        if not _platform_initialized or not _platform_supabase_client:
            logger.warning("Platform Supabase client not initialized for health check", props={})
            return False
        
        # Perform a simple query to check health
        start_time = time.time()
        try:
            # Perform a cheap no-op query to validate the connection
            if (
                _platform_supabase_client
                and await _platform_supabase_client.postgrest.rpc("health").execute()
            ):
                logger.debug(
                    "Platform Supabase client connection verified", 
                    props={}
                )
                return True
            else:
                logger.warning(
                    "Platform Supabase client connection verification failed", 
                    props={}
                )
                return False
        except Exception as e:
            logger.error("Platform Supabase health check failed: %s", e, props={})
            return False
    except Exception as e:
        logger.error("Platform Supabase health check failed: %s", e, props={})
        return False


async def close_platform_supabase() -> None:
    """
    Close the Platform Supabase client connection properly.
    This should be called during application shutdown.
    """
    global _platform_supabase_client, _platform_initialized
    
    try:
        if _platform_initialized and _platform_supabase_client:
            logger.info("Closing Platform Supabase client connection", props={})
            
            # Close the client
            if hasattr(_platform_supabase_client, "close"):
                await _platform_supabase_client.close()
            
            _platform_supabase_client = None
            _platform_initialized = False
            logger.info("Platform Supabase client connection closed", props={})
    except Exception as e:
        logger.error("Error closing Platform Supabase client: %s", e, props={})


def get_platform_supabase_stats() -> dict:
    """
    Get statistics about the Platform Supabase client for monitoring.
    
    Returns:
        dict with stats about the client
    """
    global _platform_initialized, _platform_last_health_check
    
    stats = {
        "initialized": _platform_initialized,
        "last_health_check": _platform_last_health_check,
        "time_since_last_health_check": time.time() - _platform_last_health_check if _platform_last_health_check > 0 else None,
        "health_check_interval": _platform_health_check_interval,
    }
    
    # Add client-specific stats if available
    if _platform_initialized and _platform_supabase_client:
        stats["client_available"] = True
        
        # Add connection pool stats if available
        if hasattr(_platform_supabase_client, "pool") and _platform_supabase_client.pool:
            pool = _platform_supabase_client.pool
            if hasattr(pool, "size"):
                stats["pool_size"] = pool.size
            if hasattr(pool, "max_size"):
                stats["pool_max_size"] = pool.max_size
    else:
        stats["client_available"] = False
    
    return stats
