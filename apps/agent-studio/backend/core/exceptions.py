from fastapi import HTTPException


class ServiceError(Exception):
    """Base exception for service-level errors."""

    def __init__(self, message: str, status_code: int = 500) -> None:
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

    def to_http_exception(self) -> HTTPException:
        """Convert to FastAPI HTTPException."""
        return HTTPException(status_code=self.status_code, detail=self.message)


class AuthenticationError(ServiceError):
    """Exception for authentication-related errors."""

    def __init__(self, message: str = "Authentication failed") -> None:
        super().__init__(message, status_code=401)


class AuthorizationError(ServiceError):
    """Exception for authorization-related errors."""

    def __init__(self, message: str = "Not authorized") -> None:
        super().__init__(message, status_code=403)


class ValidationError(ServiceError):
    """Exception for validation-related errors."""

    def __init__(self, message: str = "Validation failed") -> None:
        super().__init__(message, status_code=400)


class ResourceNotFoundError(ServiceError):
    """Exception for not found errors."""

    def __init__(self, message: str = "Resource not found") -> None:
        super().__init__(message, status_code=404)


class IntegrationError(ServiceError):
    """Exception for third-party integration errors."""

    def __init__(self, message: str = "Integration error occurred") -> None:
        super().__init__(message, status_code=502)


class DatabaseError(ServiceError):
    """Exception for database operation errors."""

    def __init__(self, message: str = "Database operation failed") -> None:
        super().__init__(message, status_code=500)


class AIServiceError(ServiceError):
    """Exception for AI service (OpenAI, etc.) related errors."""

    def __init__(self, message: str = "AI service error occurred") -> None:
        super().__init__(message, status_code=502)


class FileOperationError(ServiceError):
    """Exception for file operation errors."""

    def __init__(self, message: str = "File operation failed") -> None:
        super().__init__(message, status_code=500)


class WebSocketError(ServiceError):
    """Exception for WebSocket related errors."""

    def __init__(self, message: str = "WebSocket operation failed") -> None:
        super().__init__(message, status_code=500)


class MemoryOperationError(ServiceError):
    """Exception for memory operation errors."""

    def __init__(self, message: str = "Memory operation failed") -> None:
        super().__init__(message, status_code=500)


class AgentOperationError(ServiceError):
    """Exception for agent-related operation errors."""

    def __init__(self, message: str = "Agent operation failed") -> None:
        super().__init__(message, status_code=500)


class OrganizationError(ServiceError):
    """Exception for organization-related errors."""

    def __init__(self, message: str = "Organization operation failed") -> None:
        super().__init__(message, status_code=500)


class WebhookError(ServiceError):
    """Exception for webhook-related errors."""

    def __init__(self, message: str = "Webhook operation failed") -> None:
        super().__init__(message, status_code=500)


class ConfigurationError(ServiceError):
    """Exception for Redis configuration-related errors."""

    def __init__(self, message: str = "Redis configuration error occurred") -> None:
        super().__init__(message, status_code=500)


class ConnectionPoolError(ServiceError):
    """Exception for Redis connection pool-related errors."""

    def __init__(self, message: str = "Redis connection pool operation failed") -> None:
        super().__init__(message, status_code=500)


class CacheError(ServiceError):
    """Exception for Redis cache-related errors."""

    def __init__(self, message: str = "Redis cache operation failed") -> None:
        super().__init__(message, status_code=500)
