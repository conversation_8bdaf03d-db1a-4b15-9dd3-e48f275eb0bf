"""
HTTP/2 Server Configuration for Thena Agent Studio Backend

This module provides utilities to run the FastAPI application with HTTP/2 support.
"""

import asyncio
import os
import socket
import ssl
import subprocess
import tempfile
from typing import Optional, Tu<PERSON>

from fastapi import FastAPI
from hypercorn.asyncio import serve
from hypercorn.config import Config

from backend.core.logging_config import get_logger

logger = get_logger(__name__)


def find_available_port(start_port: int = 8008, max_attempts: int = 100) -> int:
    """
    Find an available port starting from start_port.

    Args:
        start_port: Port to start checking from
        max_attempts: Maximum number of ports to check

    Returns:
        An available port number

    Raises:
        RuntimeError: If no available port is found
    """
    for port in range(start_port, start_port + max_attempts):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(("localhost", port))
                logger.debug(f"Found available port: {port}", props={"port": port})
                return port
            except socket.error:
                logger.debug(
                    f"Port {port} is in use, trying next port", props={"port": port}
                )
                continue

    logger.error(
        "No available ports found",
        props={
            "start_port": start_port,
            "max_attempts": max_attempts,
            "range": f"{start_port}-{start_port + max_attempts - 1}",
        },
    )
    raise RuntimeError(
        f"No available ports found in range {start_port}-{start_port + max_attempts - 1}"
    )


def generate_self_signed_cert(cert_path: str, key_path: str) -> None:
    """
    Generate a self-signed certificate for development purposes.

    Args:
        cert_path: Path where the certificate will be saved
        key_path: Path where the private key will be saved
    """
    try:
        logger.info(
            "Generating self-signed certificate",
            props={"cert_path": cert_path, "key_path": key_path},
        )

        # Create the OpenSSL command
        cmd = [
            "openssl",
            "req",
            "-x509",
            "-newkey",
            "rsa:4096",
            "-nodes",
            "-out",
            cert_path,
            "-keyout",
            key_path,
            "-days",
            "365",
            "-subj",
            "/CN=localhost",
        ]

        # Run the OpenSSL command
        result = subprocess.run(
            cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )

        logger.debug(
            "Certificate generation successful",
            props={"stdout": result.stdout, "stderr": result.stderr},
        )
    except subprocess.CalledProcessError as e:
        logger.error(
            "Failed to generate self-signed certificate",
            props={
                "error": str(e),
                "stdout": e.stdout if hasattr(e, "stdout") else "",
                "stderr": e.stderr if hasattr(e, "stderr") else "",
            },
        )
        raise RuntimeError(f"Failed to generate self-signed certificate: {e}")
    except Exception as e:
        logger.error(
            "Unexpected error during certificate generation",
            props={"error": str(e), "error_type": type(e).__name__},
        )
        raise


def get_ssl_context(
    cert_file: Optional[str] = None, key_file: Optional[str] = None
) -> Tuple[ssl.SSLContext, str, str]:
    """
    Create an SSL context for HTTP/2 server.

    Args:
        cert_file: Path to SSL certificate file
        key_file: Path to SSL key file

    Returns:
        Tuple of (SSLContext, cert_path, key_path)
    """
    # Use provided certificate and key if available
    if (
        cert_file
        and key_file
        and os.path.exists(cert_file)
        and os.path.exists(key_file)
    ):
        logger.info(
            "Using provided SSL certificate and key",
            props={"cert_file": cert_file, "key_file": key_file},
        )
        ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        ssl_context.load_cert_chain(cert_file, key_file)
        return ssl_context, cert_file, key_file

    # Generate temporary self-signed certificate for development
    temp_dir = tempfile.mkdtemp()
    cert_path = os.path.join(temp_dir, "server.crt")
    key_path = os.path.join(temp_dir, "server.key")

    logger.info(
        "Generating temporary self-signed certificate",
        props={"temp_dir": temp_dir, "cert_path": cert_path, "key_path": key_path},
    )

    generate_self_signed_cert(cert_path, key_path)

    ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
    ssl_context.load_cert_chain(cert_path, key_path)

    # Set ALPN protocols for HTTP/2
    ssl_context.set_alpn_protocols(["h2", "http/1.1"])

    return ssl_context, cert_path, key_path


def create_hypercorn_config(app: FastAPI, port: int = 8008) -> Config:
    """
    Create a Hypercorn configuration for HTTP/2 server.

    Args:
        app: FastAPI application
        port: Port to run the server on

    Returns:
        Hypercorn Config object
    """
    config = Config()

    # Try to find an available port if the specified one is in use
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(("localhost", port))
    except socket.error:
        logger.warning(
            f"Port {port} is already in use, finding an available port",
            props={"original_port": port},
        )
        port = find_available_port(port)

    # Bind to both IPv4 and IPv6
    config.bind = [f"0.0.0.0:{port}", f"[::]:{port}"]  # IPv4  # IPv6

    config.alpn_protocols = ["h2", "http/1.1"]

    # Get SSL context
    ssl_cert_file = os.environ.get("SSL_CERT_FILE")
    ssl_key_file = os.environ.get("SSL_KEY_FILE")

    ssl_context, cert_path, key_path = get_ssl_context(ssl_cert_file, ssl_key_file)

    # Set SSL configuration
    config.certfile = cert_path
    config.keyfile = key_path

    # Set other configuration options
    config.workers = 1
    config.use_reloader = True

    logger.info(
        "Created Hypercorn configuration",
        props={
            "port": port,
            "cert_path": cert_path,
            "key_path": key_path,
            "workers": config.workers,
            "alpn_protocols": config.alpn_protocols,
            "bind": config.bind,
        },
    )

    return config


def run_http2_server(app: FastAPI, port: int = 8008) -> None:
    """
    Run the FastAPI application with HTTP/2 support using Hypercorn.

    Args:
        app: FastAPI application
        port: Port to run the server on
    """
    try:
        logger.info("Starting HTTP/2 server", props={"port": port})

        # Create Hypercorn configuration
        config = create_hypercorn_config(app, port)

        # Log server startup
        logger.info(
            "HTTP/2 server configured and starting",
            props={
                "port": port,
                "cert_file": config.certfile,
                "key_file": config.keyfile,
            },
        )

        # Run the server with asyncio
        asyncio.run(serve(app, config))
    except Exception as e:
        logger.error(
            "Failed to start HTTP/2 server",
            props={"error": str(e), "error_type": type(e).__name__},
        )

        # Provide more helpful error message
        if "ssl" in str(e).lower():
            logger.error(
                "SSL configuration error. Make sure SSL certificates are valid.",
                props={},
            )
        elif "port" in str(e).lower():
            logger.error("Port binding error. Try a different port.", props={})
        elif "hypercorn" in str(e).lower():
            logger.error(
                "Hypercorn configuration error. Check your Hypercorn installation.",
                props={},
            )

        raise
