{"name": "AI Status Change Workflow", "triggerEvent": "Ticket comment created", "filters": {"~and": [{"{{context.event.message.actor.id}}": {"~neq": "{{context.metadata.botSub}}"}}]}, "type": "AUTOMATED", "subType": "AI_AGENT", "annotations": [], "workflowDefinition": [{"stepIdentifier": 1, "activity": {"uniqueIdentifier": "ai_status_change-${app_id}-${organization_id}", "autoUpgradeToLatestVersion": true}, "input": {"id": "{{context.event.message.payload.ticket.id}}"}, "retryPolicy": {"maximumAttempts": 3, "initialInterval": 5, "backoffCoefficient": 1.5}, "onFailure": "ABORT", "executionTimeout": 150000, "isSleepActivity": false, "dependencies": [], "filters": {}}], "executingAgent": "", "teamId": "T55E0NNGDI", "metadata": {"appName": "Thena AI Status Change", "botSub": "", "teamId": "T55E0NNGDI"}}