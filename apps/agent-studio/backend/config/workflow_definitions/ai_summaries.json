{"name": "AI Ticket Summarizer Workflow", "triggerEvent": "Ticket created", "filters": {}, "type": "AUTOMATED", "subType": "AI_AGENT", "annotations": [], "workflowDefinition": [{"stepIdentifier": 1, "activity": {"uniqueIdentifier": "ai_ticket_summary-${app_id}-${organization_id}", "autoUpgradeToLatestVersion": true}, "input": {"ticket_id": "{{context.event.message.payload.ticket.id}}", "comment_id": "", "parentcommentid": ""}, "retryPolicy": {"maximumAttempts": 1, "initialInterval": 5, "backoffCoefficient": 1.5}, "onFailure": "ABORT", "executionTimeout": 150000, "isSleepActivity": false, "dependencies": [], "filters": {}}], "executingAgent": "", "teamId": "TDDY9441UP", "metadata": {"appName": "Thena AI Summarizer Final", "botSub": "", "teamId": "TDDY9441UP"}}