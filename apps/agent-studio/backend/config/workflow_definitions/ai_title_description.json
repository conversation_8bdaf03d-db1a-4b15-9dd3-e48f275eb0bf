{"name": "AI Title Description Generator Workflow", "triggerEvent": "Ticket created", "filters": {}, "type": "AUTOMATED", "subType": "AI_AGENT", "annotations": [], "workflowDefinition": [{"stepIdentifier": 1, "activity": {"uniqueIdentifier": "ai_title_description_generator-${app_id}-${organization_id}", "autoUpgradeToLatestVersion": true}, "input": {"ticket_id": "{{context.event.message.payload.ticket.id}}", "comment_id": "", "parentcommentid": ""}, "retryPolicy": {"maximumAttempts": 3, "initialInterval": 5, "backoffCoefficient": 1.5}, "onFailure": "ABORT", "executionTimeout": 150000, "isSleepActivity": false, "dependencies": [], "filters": {}}], "executingAgent": "", "teamId": "TDDY9441UP", "metadata": {"appName": "Thena AI Title Description Generator", "botSub": "", "teamId": "TDDY9441UP"}}