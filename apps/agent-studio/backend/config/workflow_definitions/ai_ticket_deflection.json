{"name": "AI Ticket Deflection Workflow", "triggerEvent": "Ticket comment created", "filters": {"~and": [{"{{context.event.message.actor.id}}": {"~neq": "{{context.metadata.botSub}}"}}, {"{{ context.event.message.payload.ticket.assignedAgent.id }}": {"~eq": "{{context.metadata.botSub}}"}}, {"{{context.event.message.payload.comment.customerContact}}": {"~isempty": false}}]}, "type": "AUTOMATED", "subType": "AI_AGENT", "annotations": [{"relations": ["assignedAgent"]}], "workflowDefinition": [{"stepIdentifier": 1, "activity": {"uniqueIdentifier": "ai_ticket_deflection-${app_id}-${organization_id}", "autoUpgradeToLatestVersion": true}, "input": {"ticket_id": "{{context.event.message.payload.ticket.id}}", "parentcommentid": "{{context.event.message.payload.comment.parentCommentId}}", "comment_id": "{{context.event.message.payload.comment.id}}"}, "retryPolicy": {"maximumAttempts": 3, "initialInterval": 5, "backoffCoefficient": 1.5}, "onFailure": "ABORT", "executionTimeout": 150000, "isSleepActivity": false, "dependencies": [], "filters": {}}], "executingAgent": "", "teamId": "T55E0NNGDI", "metadata": {"appName": "Thena AI Status Change", "botSub": "", "teamId": "T55E0NNGDI"}}