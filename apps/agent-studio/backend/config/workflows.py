"""
Configuration file for default workflows that are created during installation.
Each workflow is defined with its trigger event, filters, and workflow definition.
"""

import json
import os


# Helper function to load workflows from JSON files
def _load_workflow_json(filename):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_path = os.path.join(current_dir, "workflow_definitions", filename)
    with open(json_path, "r") as f:
        return json.load(f)


# Load workflows from JSON files
_ai_title_description = _load_workflow_json("ai_title_description.json")
_ai_summaries = _load_workflow_json("ai_summaries.json")
_ai_status_change = _load_workflow_json("ai_status_change.json")
_ai_ticket_deflection = _load_workflow_json("ai_ticket_deflection.json")
_ai_ticket_deflection_2 = _load_workflow_json("ai_ticket_deflection_2.json")
_ai_title_description_2 = _load_workflow_json("ai_title_description_2.json")
_ai_summaries_2 = _load_workflow_json("ai_summaries_2.json")

# Define workflows
WORKFLOWS = {
    "ai-title-description": [_ai_title_description, _ai_title_description_2],
    "ai-summaries": [_ai_summaries, _ai_summaries_2],
    "ai-status-change": [_ai_status_change],
    "ai-ticket-deflection": [_ai_ticket_deflection, _ai_ticket_deflection_2],
}
