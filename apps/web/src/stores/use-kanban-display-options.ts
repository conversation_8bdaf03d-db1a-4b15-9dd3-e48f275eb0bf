import { create } from "zustand";
import { CustomField } from "../types/accounts";

export interface CardField {
  id: string;
  name: string;
  visible: boolean;
  subfields?: {
    id: string;
    name: string;
    visible: boolean;
  }[];
  selectedSubfields?: string[];
}

export interface DisplayOptionsState {
  showClosedRequests: {
    enabled: boolean;
    duration:
      | "Past week"
      | "Past month"
      | "Past 3 months"
      | "Past year"
      | "All time"
      | string;
  };
  displayTimeOnCard: "Created" | "Updated";
  showDiscardedRequests: boolean;
  showSnoozedRequests: boolean;
  pivotBoard: {
    columns: "Status" | "Priority" | "Assignee" | "None";
    rows: "Status" | "Priority" | "Assignee" | "None";
  };
  cardFields: CardField[];
  selectedAccountFields: string[];
  updateShowClosedRequests: (
    enabled: boolean,
    duration:
      | "Past week"
      | "Past month"
      | "Past 3 months"
      | "Past year"
      | "All time"
      | string,
  ) => void;
  updateDisplayTimeOnCard: (value: "Created" | "Updated") => void;
  updateShowDiscardedRequests: (value: boolean) => void;
  updateShowSnoozedRequests: (value: boolean) => void;
  updatePivotBoard: (field: "columns" | "rows", value: string) => void;
  updateCardFields: (newFields: CardField[]) => void;
  toggleFieldVisibility: (fieldId: string) => void;
  updateSelectedAccountFields: (fields: string[]) => void;
  updateSelectedSubfields: (fieldId: string, subfields: string[]) => void;
  setStateOverwrite: (newState: DisplayOptionsState) => void;
  updateAccountCustomFields: (customFields: CustomField[]) => void;
}

export const useKanbanDisplayOptions = create<DisplayOptionsState>()((set) => ({
  showClosedRequests: {
    enabled: true,
    duration: "Past month",
  },
  displayTimeOnCard: "Created",
  showDiscardedRequests: false,
  showSnoozedRequests: false,
  pivotBoard: {
    columns: "Status",
    rows: "None",
  },
  cardFields: [
    {
      id: "slas",
      name: "SLAs",
      visible: true,
      subfields: null,
      selectedSubfields: null,
    },
    {
      id: "tags",
      name: "Tags",
      visible: true,
      subfields: null,
      selectedSubfields: null,
    },
    {
      id: "account-fields",
      name: "Account fields",
      visible: true,
      subfields: [
        // { id: "status", name: "Status", visible: true },
        { id: "classification", name: "Classification", visible: true },
        { id: "health", name: "Health", visible: true },
        { id: "industry", name: "Industry", visible: true },
        { id: "primary_domain", name: "Primary domain", visible: true },
        { id: "secondary_domain", name: "Secondary domain", visible: true },
        { id: "account_owner", name: "Account owner", visible: true },
        { id: "annual_revenue", name: "Annual revenue", visible: true },
        { id: "employees", name: "Employees", visible: true },
        { id: "website", name: "Website", visible: true },
        { id: "billing_address", name: "Billing address", visible: true },
        { id: "shipping_address", name: "Shipping address", visible: true },
      ],
      selectedSubfields: ["name"],
    },
  ],
  selectedAccountFields: ["account-status", "score", "industry", "plan"],
  updateShowClosedRequests: (enabled, duration) =>
    set((state) => ({
      showClosedRequests: {
        ...state.showClosedRequests,
        enabled,
        duration: duration as
          | "Past week"
          | "Past month"
          | "Past 3 months"
          | "Past year"
          | "All time",
      },
    })),
  updateDisplayTimeOnCard: (value) => set({ displayTimeOnCard: value }),
  updateShowDiscardedRequests: (value) => set({ showDiscardedRequests: value }),
  updateShowSnoozedRequests: (value) => set({ showSnoozedRequests: value }),
  updatePivotBoard: (field, value) =>
    set((state) => ({
      pivotBoard: { ...state.pivotBoard, [field]: value },
    })),
  updateCardFields: (newFields) => set({ cardFields: newFields }),
  toggleFieldVisibility: (fieldId) =>
    set((state) => ({
      cardFields: state.cardFields.map((field) =>
        field.id === fieldId ? { ...field, visible: !field.visible } : field,
      ),
    })),
  updateSelectedAccountFields: (fields) =>
    set({ selectedAccountFields: fields }),
  updateSelectedSubfields: (fieldId, subfields) =>
    set((state) => ({
      cardFields: state.cardFields.map((field) =>
        field.id === fieldId
          ? { ...field, selectedSubfields: subfields }
          : field,
      ),
    })),

  setStateOverwrite: (newState) => set(newState),

  updateAccountCustomFields: (customFields) =>
    set((state) => {
      const accountFieldsIndex = state.cardFields.findIndex(
        (field) => field.id === "account-fields",
      );

      if (accountFieldsIndex === -1) return state;

      const accountFields = state.cardFields[accountFieldsIndex];
      const existingSubfields = accountFields.subfields || [];

      // Filter out existing custom fields (those with id starting with "custom_")
      const nonCustomFields = existingSubfields.filter(
        (field) => !field.id.startsWith("custom_"),
      );

      // Create new custom subfields from the provided customFields
      const newCustomSubfields = customFields.map((field) => ({
        id: `custom_${field.id}`,
        name: field.name,
        options: field.options,
        visible: true,
      }));

      const updatedCardFields = [...state.cardFields];
      updatedCardFields[accountFieldsIndex] = {
        ...accountFields,
        subfields: [...nonCustomFields, ...newCustomSubfields],
      };

      return {
        ...state,
        cardFields: updatedCardFields,
      };
    }),
}));
