"use client";

import { useQuery } from "@tanstack/react-query";
import { MoveUpRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { WebChatAgentBanner } from "../../../../../../components/agents/web-chat-agent-banner";
import CommonSelectWrapper from "../../../../../../components/common-select-wrapper";
import type { SelectOptionType } from "../../../../../../components/common-select-wrapper/types";
import { Button } from "../../../../../../components/ui/button";
import { AGENT_NAME } from "../../../../../../constants/web-chat";
import { getAgents } from "../../../../../../lib/api/agents";
import { useTicketMetaStore } from "../../../../../../store/ticket-meta-store";
interface App {
  uid: string;
  name: string;
  description: string;
  manifest: {
    app: {
      icons: {
        small: string;
        large: string;
      };
    };
    developer: {
      name: string;
      privacy_policy_url?: string;
      terms_url?: string;
    };
    metadata?: {
      template_id?: string;
    };
    activities?: Array<{
      name: string;
      description: string;
    }>;
  };
  isInstalled: boolean;
  template?: {
    id: string;
    title: string;
    category: string;
    description: string;
    metadata: {
      rating?: number;
      pricing?: {
        yearly?: number;
        monthly?: number;
      };
    };
    capabilities?: string[];
    configuration?: {
      goal?: string;
      role?: string;
      tone?: string;
      personality_traits?: string[];
    };
    avatar_url?: string;
    comingSoon?: boolean;
  };
}

const fetchApps = async ({
  category,
  page = 1,
  limit = 50, // Increased limit to ensure we get all the apps we need to transform
}: {
  category?: string;
  page?: number;
  limit?: number;
}): Promise<{
  data: App[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}> => {
  const params = new URLSearchParams();
  params.append("page", page.toString());
  params.append("limit", limit.toString());
  if (category && category !== "all") params.append("category", category);

  const response = await fetch(`/api/marketplace?${params.toString()}`);
  if (!response.ok) {
    throw new Error("Failed to fetch apps");
  }
  return response.json();
};

export default function WebChatSettingsPage() {
  const [selectedTeam, setSelectedTeam] = useState<SelectOptionType | null>(
    null,
  );
  const [isLoadingDeployments, setIsLoadingDeployments] = useState(true);

  const [allowedDomains, setAllowedDomains] = useState<string[]>([]);
  const teams = useTicketMetaStore((state) => state.teams);
  const validDomains = allowedDomains.filter((domain) => domain.trim() !== "");
  const [currentDeployedTeam, setCurrentDeployedTeam] = useState(null);
  const { data: appsData, refetch } = useQuery<{
    data: App[];
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }>({
    queryKey: ["marketplace-apps", "all"],
    queryFn: () =>
      fetchApps({
        category: "all",
      }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    enabled: typeof window !== "undefined",
  });

  const appDetails = useMemo(() => {
    return appsData?.data.find((app) => app.name === AGENT_NAME);
  }, [appsData]);

  const router = useRouter();

  const { data: agents } = useQuery({
    queryKey: ["agents"],
    queryFn: () => getAgents(),
    refetchInterval: 10000, // Add polling every 10 seconds
    staleTime: 5000, // Consider data stale after 5 seconds
    enabled: !!appDetails?.uid,
  });
  const agentDetails = useMemo(() => {
    return agents?.find((agent) => agent.name === AGENT_NAME);
  }, [agents, appDetails, selectedTeam]);

  // Fetch existing deployments when component mounts
  useEffect(() => {
    async function fetchDeployments() {
      setIsLoadingDeployments(true);
      try {
        const response = await fetch(
          `/api/agents/${agentDetails?.id}/deployments`,
        );

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Failed to fetch deployments", errorData);
          setIsLoadingDeployments(false);
          return;
        }

        const deployments = await response.json();
        // If we have deployments for this agent, use the first one
        if (deployments && deployments.length > 0) {
          const latestDeployment = deployments[0];
          // If the deployment has allowed origins, update state
          if (
            latestDeployment.allowed_origins &&
            latestDeployment.allowed_origins.length > 0
          ) {
            setAllowedDomains(latestDeployment.allowed_origins);
          }

          // Try to set selected team based on team_id if available
          if (latestDeployment.team_id) {
            // You might need to fetch team details or handle this differently
            // For now, we'll create a minimal team object
            setCurrentDeployedTeam(latestDeployment.team_id);
            setSelectedTeam({
              label: teams.find((item) => item.uid === latestDeployment.team_id)
                ?.name,
              value: latestDeployment.team_id,
              heroIcon: {
                name: teams.find(
                  (item) => item.uid === latestDeployment.team_id,
                )?.icon,
                color: teams.find(
                  (item) => item.uid === latestDeployment.team_id,
                )?.color,
              },
            });
          }
        }
      } catch (err) {
        console.error("Error fetching deployments:", err);
      } finally {
        setIsLoadingDeployments(false);
      }
    }
    if (agentDetails?.id) {
      fetchDeployments();
    }
  }, [agentDetails?.id]);

  const handleHiring = async () => {
    try {
      const response = await fetch("/api/marketplace/install", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          appId: appDetails?.uid,
          teamIds: [selectedTeam?.value],
          appConfiguration: {
            required_settings: [],
            optional_settings: [],
          },
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to install app");
      }

      await refetch();
      await handleDeployment();
    } catch (e) {
      console.error("Installation error:", e);
      toast.error(
        `Failed to hire ${AGENT_NAME}: ${
          e instanceof Error ? e.message : "Unknown error"
        }`,
      );
    }
  };
  const handleDeployment = async () => {
    try {
      const response = await fetch(
        `/api/agents/${agentDetails.id}/deployments`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            team_id: selectedTeam?.value,
            allowed_origins: validDomains, // Send only valid domains
            deployment_type: "widget",
          }),
        },
      );

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Deployment failed");
      }

      toast.success(
        "Deployment successful! Your widget configuration is ready.",
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred.";
      toast.error(errorMessage);
    }
  };
  const handleBtnCLick = async () => {
    if (currentDeployedTeam !== selectedTeam?.value) {
      await handleDeployment();
    } else {
      await handleHiring();
    }
  };

  return (
    <div className="h-[calc(100vh-4rem)] flex flex-col">
      <div className="px-6 pt-8 h-full">
        <div className="grid gap-4 mx-auto max-w-[640px]">
          <div>
            <h2 className="text-2xl font-medium">Web chat</h2>
            <p className="text-sm text-[var(--color-text-muted)]">
              Embed support chat directly on your website.
            </p>
          </div>
          <div className="border-border border border-solid rounded-sm w-full p-4">
            <label className="text-sm text-[var(--color-text-muted)]">
              Select team
            </label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Choose which team will handle web chat conversation.
            </p>
            <div className="flex items-center justify-between">
              <CommonSelectWrapper
                key="Select team"
                placeholder="Select team"
                hideClearIndicator
                isClearable={false}
                options={teams?.map((option) => ({
                  value: option.uid,
                  label: option.name,
                  heroIcon: {
                    name: option.icon || "RocketLaunchIcon",
                    color: option.color || "purple",
                  },
                }))}
                name="Select team"
                onCancelClick={() => setSelectedTeam(null)}
                value={selectedTeam}
                isVirtualized={true}
                isOptionsMemoized
                onChange={(value) => setSelectedTeam(value as SelectOptionType)}
                maxVirtuosoHeight={600}
                triggerClassname="w-full h-6"
                wrapperClassname="!max-w-[80%] w-full"
                isLoading={!!selectedTeam?.value && isLoadingDeployments}
                disabled={!!selectedTeam?.value && isLoadingDeployments}
              />
              {(agentDetails === undefined ||
                currentDeployedTeam !== selectedTeam?.value) && (
                <Button
                  variant="default"
                  size="sm"
                  className="gap-2"
                  onClick={handleBtnCLick}
                  disabled={!selectedTeam}
                >
                  Add to team
                </Button>
              )}
            </div>
            {selectedTeam && agentDetails !== undefined && (
              <Button
                size="sm"
                className="mt-3"
                onClick={() => {
                  router.push(
                    `/dashboard/${selectedTeam.value}/settings/sources/web-chat?agentId=${agentDetails?.id}`,
                  );
                }}
              >
                Configure deployment in team <MoveUpRight />
              </Button>
            )}
          </div>
          {selectedTeam && (
            <WebChatAgentBanner
              AGENT_NAME={AGENT_NAME}
              agentDetails={agentDetails}
            />
          )}
        </div>
      </div>
    </div>
  );
}
