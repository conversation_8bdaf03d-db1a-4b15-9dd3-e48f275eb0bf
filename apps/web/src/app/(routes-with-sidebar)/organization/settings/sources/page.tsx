"use client";

import MsTeamsColouredIcon from "@/components/icons/MsTeamsColouredIcon";
import SlackColoredIcon from "@/components/icons/SlackColoredIcon";
import Thena<PERSON>oader from "@/components/thena-loader";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { SourceTypes } from "@/types/sources";
import { useQuery } from "@tanstack/react-query";
import { CheckCircle2, Loader2, Mail, Settings, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import WebChatIcon from "../../../../../components/icons/web-chat-icon";
import { AGENT_NAME } from "../../../../../constants/web-chat";
import { getAgents } from "../../../../../lib/api/agents";
import { App } from "../../../../../store/installation-form-store";

interface Source {
  id: string;
  name: string;
  botUserId: string;
  description: string;
  isConnected: boolean;
  type: SourceTypes;
  connectionDetails?: {
    workspaceName?: string;
    connectedAt?: string;
  };
}
const fetchApps = async ({
  category,
  page = 1,
  limit = 50, // Increased limit to ensure we get all the apps we need to transform
}: {
  category?: string;
  page?: number;
  limit?: number;
}): Promise<{
  data: App[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}> => {
  const params = new URLSearchParams();
  params.append("page", page.toString());
  params.append("limit", limit.toString());
  if (category && category !== "all") params.append("category", category);

  const response = await fetch(`/api/marketplace?${params.toString()}`);
  if (!response.ok) {
    throw new Error("Failed to fetch apps");
  }
  return response.json();
};

export default function SourcesSettings() {
  const router = useRouter();
  const [sources, setSources] = useState<Source[]>([
    {
      id: "email",
      name: "Email",
      botUserId: "",
      description: "Configure your email settings",
      isConnected: true,
      type: "email" as SourceTypes,
    },
    {
      id: "web-chat",
      name: "Web chat",
      botUserId: "",
      description: "Configure your web-chat settings",
      isConnected: false,
      type: "web-chat" as SourceTypes,
    },
  ]);
  const [loading, setLoading] = useState(false);
  const [installing, setInstalling] = useState<string | null>(null);

  const { data: appsData } = useQuery<{
    data: App[];
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }>({
    queryKey: ["marketplace-apps", "all"],
    queryFn: () =>
      fetchApps({
        category: "all",
      }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    enabled: typeof window !== "undefined",
  });

  const appDetails = useMemo(() => {
    return appsData?.data.find((app) => app.name === AGENT_NAME);
  }, [appsData]);
  const { data: agents } = useQuery({
    queryKey: ["agents"],
    queryFn: () => getAgents(),
    refetchInterval: 10000, // Add polling every 10 seconds
    staleTime: 5000, // Consider data stale after 5 seconds
    enabled: !!appDetails?.uid,
  });
  const agentDetails = useMemo(() => {
    return agents?.find((agent) => agent.name === AGENT_NAME);
  }, [agents, appDetails]);

  useEffect(() => {
    const fetchSources = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/workspace/sources`);
        if (!response.ok) {
          throw new Error("Failed to fetch sources");
        }
        const data = await response.json();
        setSources((prevSources) => {
          const emailSource = prevSources.find((s) => s.type === "email");
          const webChatSource = prevSources.find((s) => s.type === "web-chat");
          const otherSources = data.sources.filter(
            (s) => s.type !== "email" && s.type !== "web-chat",
          );
          return emailSource
            ? [emailSource, webChatSource, ...otherSources]
            : [...prevSources, ...otherSources];
        });
      } catch (_error) {
        toast.error("Failed to load sources");
      } finally {
        setLoading(false);
      }
    };

    fetchSources();
  }, []);

  useEffect(() => {
    async function fetchDeployments() {
      try {
        const response = await fetch(
          `/api/agents/${agentDetails?.id}/deployments`,
        );

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Failed to fetch deployments", errorData);
          return;
        }

        const deployments = await response.json();
        // If we have deployments for this agent, use the first one
        if (deployments && deployments.length > 0) {
          const latestDeployment = deployments[0];
          if (latestDeployment) {
            setSources((prevSources) =>
              prevSources.map((s) =>
                s.type === "web-chat" ? { ...s, isConnected: true } : s,
              ),
            );
          }
        }
      } catch (err) {
        console.error("Error fetching deployments:", err);
      }
    }
    if (agentDetails?.id) {
      fetchDeployments();
    }
  }, [agentDetails?.id]);

  const getSourceIcon = (type: Source["type"]) => {
    switch (type) {
      case "slack":
        return (
          <div className="flex items-center justify-center w-6 h-6">
            <SlackColoredIcon size={24} />
          </div>
        );
      case "ms_teams":
        return (
          <div className="flex items-center justify-center w-6 h-6">
            <MsTeamsColouredIcon size={24} />
          </div>
        );
      case "email":
        return <Mail className="w-6 h-6" strokeWidth={1.5} />;
      case "web-chat":
        return <WebChatIcon className="w-6 h-6" />;
      default:
        return null;
    }
  };

  const handleConnect = async (sourceId: string) => {
    try {
      setInstalling(sourceId);
      const response = await fetch(`/api/apps/install`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ appId: sourceId }),
      });

      if (!response.ok) {
        throw new Error("Failed to install app");
      }

      const data = await response.json();
      if (data.authorizationUrl) {
        router.push(data.authorizationUrl);
      } else {
        // Refetch sources after successful installation
        const sourcesResponse = await fetch(`/api/workspace/sources`);
        if (!sourcesResponse.ok) {
          throw new Error("Failed to fetch updated sources");
        }
        const sourcesData = await sourcesResponse.json();
        setSources((prevSources) => {
          const emailSource = prevSources.find((s) => s.type === "email");
          const webChatSource = prevSources.find((s) => s.type === "web-chat");
          const otherSources = sourcesData.sources.filter(
            (s) => s.type !== "email" && s.type !== "web-chat",
          );
          return emailSource
            ? [emailSource, webChatSource, ...otherSources]
            : [...prevSources, ...otherSources];
        });

        // Navigate to the source details page
        const installedSource = sourcesData.sources.find(
          (s: Source) => s.id === sourceId,
        );
        if (installedSource) {
          router.push(
            `/organization/settings/sources/${sourceId}?botUserId=${installedSource.botUserId}&type=${installedSource.type}`,
          );
        }
      }
    } catch (error) {
      console.error("Error installing app:", error);
      toast.error("Failed to connect to the source");
    } finally {
      setInstalling(null);
    }
  };

  if (loading) {
    return (
      <div className="h-full w-full flex items-center justify-center min-h-[640px]">
        <ThenaLoader loaderText="Loading sources..." />
      </div>
    );
  }
  return (
    <main className="flex-1 overflow-y-auto">
      <div className="container mx-auto pt-[56px] pb-6">
        <div className="max-w-[640px] mx-auto">
          <div className="flex flex-col gap-6">
            <div>
              <h1 className="text-2xl font-medium">Sources</h1>
              <p className="text-sm text-muted-foreground mt-1">
                Connect and manage your communication channels.
              </p>
            </div>
            <div className="flex flex-col gap-4">
              {sources.map((source) => (
                <Card
                  key={source.id}
                  className={cn(
                    "relative overflow-hidden transition-all duration-200 rounded-sm border shadow-none",
                    source.isConnected
                      ? "hover:border-accent-foreground/20 hover:bg-accent/10 hover:shadow-sm"
                      : "bg-muted/30 hover:bg-muted/40 hover:border-muted-foreground/20 hover:shadow-sm",
                  )}
                >
                  <div className="p-4">
                    <div className="grid grid-cols-[auto_1fr_auto] gap-3 items-center">
                      <div className="flex items-center justify-center">
                        {getSourceIcon(source.type)}
                      </div>

                      <div className="flex flex-col justify-center">
                        <div className="flex items-center gap-2 flex-wrap">
                          <span className="text-lg font-medium">
                            {source.name}
                          </span>
                          {source.isConnected ? (
                            <span className="flex items-center gap-1.5 text-xs font-medium px-1.5 py-0.5 rounded-[4px] bg-green-100 dark:bg-green-950 text-green-700 dark:text-green-300">
                              <CheckCircle2
                                className="w-3 h-3"
                                strokeWidth={1.5}
                              />
                              Enabled
                              {source.connectionDetails?.connectedAt && (
                                <span className="font-normal">
                                  {new Date(
                                    source.connectionDetails.connectedAt,
                                  ).toLocaleDateString()}
                                </span>
                              )}
                            </span>
                          ) : (
                            <span className="flex items-center gap-1.5 text-xs font-medium px-1.5 py-0.5 rounded-[4px] bg-muted text-muted-foreground">
                              <XCircle className="w-3 h-3" strokeWidth={1.5} />
                              Not connected
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="self-center">
                        <Button
                          variant={source.isConnected ? "outline" : "default"}
                          className="flex items-center gap-2 shrink-0"
                          disabled={installing === source.id}
                          onClick={() => {
                            if (source.type === "email") {
                              router.push(
                                "/organization/settings/sources/email",
                              );
                            } else if (source.type === "web-chat") {
                              router.push(
                                "/organization/settings/sources/web-chat",
                              );
                            } else if (source.isConnected) {
                              router.push(
                                `/organization/settings/sources/${source.id}?botUserId=${source.botUserId}&type=${source.type}`,
                              );
                            } else {
                              handleConnect(source.id);
                            }
                          }}
                        >
                          {source.isConnected ? (
                            <>
                              <Settings className="w-4 h-4" strokeWidth={1.5} />
                              Manage connection
                            </>
                          ) : installing === source.id ? (
                            <>
                              <Loader2 className="w-4 h-4 animate-spin" />
                              Connecting...
                            </>
                          ) : (
                            "Enable"
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
