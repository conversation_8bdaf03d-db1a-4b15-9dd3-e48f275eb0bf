"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Activity, Event } from "../../../../../../../types/workflow";
import { FilterBuilder } from "./FilterBuilder";
import { RichTextEditor } from "./RichTextEditor";

interface MessageContent {
  content: string;
  contentHtml: string;
}

interface FilterCondition {
  [key: string]: {
    [operator: string]: unknown;
  };
}

interface Rule {
  name: string;
  trigger: string;
  considerations: string[];
  filters: { "~and": FilterCondition[] };
  message: MessageContent;
  enabled: boolean;
  uniqueIdentifier: string;
}

interface RuleModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  initialRule?: Record<string, unknown>;
  fetchRules: () => Promise<void>;
  isEdit?: boolean;
}

const DEFAULT_RULE: Rule = {
  name: "",
  trigger: "ticket_created",
  considerations: [],
  filters: { "~and": [] },
  message: {
    content: "",
    contentHtml: "",
  },
  enabled: true,
  uniqueIdentifier: "",
};

export function RuleModal({
  isOpen,
  onOpenChange,
  initialRule,
  fetchRules,
  isEdit = false,
}: RuleModalProps) {
  const [rule, setRule] = useState<Rule>(DEFAULT_RULE);
  const [events, setEvents] = useState<Event[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const params = useParams();
  const teamId = params.teamId as string;
  // Update useEffect to only run when modal opens
  // Update the useEffect in RuleModal.js that initializes the rule

  useEffect(() => {
    if (isOpen) {
      if (initialRule) {
        // Initialize a valid filters object with ~and property
        let filters = { "~and": [] };

        // Safely extract filters from initialRule
        if (initialRule.filters) {
          if (Array.isArray(initialRule.filters)) {
            // Handle case where filters might be an array
            filters = { "~and": initialRule.filters };
          } else if (
            typeof initialRule.filters === "object" &&
            initialRule.filters["~and"] &&
            Array.isArray(initialRule.filters["~and"])
          ) {
            // Handle case where filters is an object with ~and property
            filters = { "~and": initialRule.filters["~and"] };
          } else if (typeof initialRule.filters === "object") {
            // Handle other object cases by wrapping with ~and
            filters = { "~and": [] };
          }
        }
        setRule({
          name: (initialRule.name as string) || "",
          trigger: (initialRule.trigger as string) || "ticket_created",
          considerations: Array.isArray(initialRule.considerations)
            ? initialRule.considerations
            : [],
          filters: filters, // This is now guaranteed to have the ~and property
          message: (initialRule.message as MessageContent) || {
            content: "",
            contentHtml: "",
          },
          enabled:
            typeof initialRule.enabled === "boolean"
              ? initialRule.enabled
              : true,
          uniqueIdentifier: (initialRule.uniqueIdentifier as string) || "",
        });
      } else {
        setRule(DEFAULT_RULE);
      }
    }
  }, [isOpen, initialRule]);

  const [errors, setErrors] = useState<{
    name?: string;
    trigger?: string;
    message?: string;
  }>({});

  const [touched, setTouched] = useState<{
    name: boolean;
    message: boolean;
  }>({
    name: false,
    message: false,
  });

  const backendToLogical: Record<string, string> = {
    "=": "~eq",
    "!=": "~neq",
    in: "~in",
    "not in": "~nin",
    contains: "~contains",
    "not contains": "~ncontains",
    ">": "~gt",
    ">=": "~gte",
    "<": "~lt",
    "<=": "~lte",
    "is null": "~isNull",
    "is not null": "~isNotNull",
    "is true": "~isTrue",
    "is false": "~isFalse",
  };

  const handleChange = (field: string, value: unknown) => {
    if (field === "message") {
      setRule((prev: Rule) => ({
        ...prev,
        message: value as MessageContent,
      }));
    } else {
      setRule((prev: Rule) => ({
        ...prev,
        [field]: value,
      }));
    }

    // Clear error when field is changed
    if (errors[field as keyof typeof errors]) {
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }

    // Mark field as touched
    if (field === "name" || field === "message") {
      setTouched((prev) => ({
        ...prev,
        [field]: true,
      }));
    }
  };

  const handleConsiderationChange = (value: string[]) => {
    setRule((prev: Rule) => ({
      ...prev,
      considerations: value,
    }));
  };

  const validateForm = () => {
    const newErrors: {
      name?: string;
      trigger?: string;
      message?: string;
    } = {};

    // Mark all fields as touched during validation
    setTouched({
      name: true,
      message: true,
    });

    if (!String(rule.name).trim()) {
      newErrors.name = "Rule name is required.";
    }

    if (!rule.trigger) {
      newErrors.trigger = "Trigger is required.";
    }

    if (
      !rule.message.content.trim() &&
      !rule.message.contentHtml.trim()
    ) {
      newErrors.message = "Message is required.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateWorkflow = async (payload) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/workflows`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        let message = "Failed to create workflow";
        try {
          const errorPayload = await response.json();
          if (errorPayload?.error) message = errorPayload.error;
          if (errorPayload?.message) message = errorPayload.message;
        } catch (_) {
          /* non-JSON response – fall back to default message */
        }
        throw new Error(message);
      }
    } catch (error) {
      console.error("[handleCreateWorkflow] Error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create workflow",
      );
    } finally {
      fetchRules();
    }
  };

  const handleUpdateWorkflow = async (payload) => {
    try {
      const response = await fetch(
        `/api/teams/${teamId}/workflows/${rule.uniqueIdentifier}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        },
      );

      if (!response.ok) {
        let message = "Failed to update workflow";
        try {
          const errorPayload = await response.json();
          if (errorPayload?.error) message = errorPayload.error;
          if (errorPayload?.message) message = errorPayload.message;
        } catch (_) {
          /* non-JSON response – fall back to default message */
        }
        throw new Error(message);
      }
    } catch (error) {
      console.error("[handleCreateWorkflow] Error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create workflow",
      );
    } finally {
      fetchRules();
    }
  };

  const getWorkflowDefinition = (pathToAnnotate, filtersOr) => {
    if (rule.trigger === "ticket_created") {
      return [
        ...(rule.considerations as string[]).map((consideration, index) => {
          const activity = activities.find((act) => {
            switch (consideration) {
              case "nonWorkingHours":
              case "groupUnavailableWorkingHours":
              case "holidays":
              case "groupUnavailableHolidays":
                return (
                  act.uniqueIdentifier ===
                  "teams:check-team-availability-platform"
                );
              case "memberUnavailableTimeOff":
              case "memberUnavailableWorkingHours":
                return (
                  act.uniqueIdentifier ===
                  "users:check-user-availability-platform"
                );
              default:
                return false;
            }
          });
          return {
            stepIdentifier: index + 1,
            activity: {
              uniqueIdentifier: activity?.uniqueIdentifier || "",
              autoUpgradeToLatestVersion: true,
            },
            input:
              consideration === "groupUnavailableWorkingHours" ||
              consideration === "groupUnavailableHolidays"
                ? {
                    teamId: `{{${pathToAnnotate}.subTeam.id}}`,
                  }
                : consideration === "memberUnavailableWorkingHours" ||
                  consideration === "memberUnavailableTimeOff"
                ? {
                    userId: `{{${pathToAnnotate}.assignedAgent.id}}`,
                  }
                : {
                    teamId: `{{${pathToAnnotate}.team.id}}`,
                  },
          };
        }),
        {
          stepIdentifier: (rule.considerations as string[]).length + 1,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            ...rule.message,
            commentVisibility: "public",
            commentType: "comment",
            entityType: "ticket",
            entityId: `{{${pathToAnnotate}.id}}`,
            parentCommentId: "{{context.event.message.payload.comment.id}}",
          },
          dependencies: (rule.considerations as string[]).map(
            (_, index) => index + 1,
          ),
          ...(filtersOr.length
            ? {
                filters: {
                  "~or": filtersOr,
                },
              }
            : {}),
        },
      ];
    } else {
      return [
        ...(rule.considerations as string[]).map((consideration, index) => {
          const activity = activities.find((act) => {
            switch (consideration) {
              case "nonWorkingHours":
              case "groupUnavailableWorkingHours":
              case "holidays":
              case "groupUnavailableHolidays":
                return (
                  act.uniqueIdentifier ===
                  "teams:check-team-availability-platform"
                );
              case "memberUnavailableTimeOff":
              case "memberUnavailableWorkingHours":
                return (
                  act.uniqueIdentifier ===
                  "users:check-user-availability-platform"
                );
              default:
                return false;
            }
          });
          return {
            stepIdentifier: index + 1,
            activity: {
              uniqueIdentifier: activity?.uniqueIdentifier || "",
              autoUpgradeToLatestVersion: true,
            },
            input:
              consideration === "groupUnavailableWorkingHours" ||
              consideration === "groupUnavailableHolidays"
                ? {
                    teamId: `{{${pathToAnnotate}.subTeam.id}}`,
                  }
                : consideration === "memberUnavailableWorkingHours" ||
                  consideration === "memberUnavailableTimeOff"
                ? {
                    userId: `{{${pathToAnnotate}.assignedAgent.id}}`,
                  }
                : {
                    teamId: `{{${pathToAnnotate}.team.id}}`,
                  },
          };
        }),
        {
          stepIdentifier: (rule.considerations as string[]).length + 1,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            ...rule.message,
            commentVisibility: "public",
            commentType: "comment",
            entityType: "ticket",
            entityId: `{{${pathToAnnotate}.id}}`,
            parentCommentId: "{{context.event.message.payload.comment.id}}",
          },
          dependencies: (rule.considerations as string[]).map(
            (_, index) => index + 1,
          ),
          filters: {
            "~and": [
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isempty": true,
                },
              },
              ...(filtersOr.length ? [{ "~or": filtersOr }] : []),
            ],
          },
        },
        {
          stepIdentifier: (rule.considerations as string[]).length + 2,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            ...rule.message,
            commentVisibility: "public",
            commentType: "comment",
            entityType: "ticket",
            entityId: `{{${pathToAnnotate}.id}}`,
            parentCommentId:
              "{{context.event.message.payload.comment.parentCommentId}}",
          },
          dependencies: (rule.considerations as string[]).map(
            (_, index) => index + 1,
          ),
          filters: {
            "~and": [
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isempty": false,
                },
              },
              ...(filtersOr.length ? [{ "~or": filtersOr }] : []),
            ],
          },
        },
      ];
    }
  };
  const handleSave = () => {
    if (validateForm()) {
      onOpenChange(false);
      const pathToAnnotate =
        rule.trigger === "ticket_created"
          ? events.find((item) => item.eventType === "ticket:created")?.metadata
              ?.pathToAnnotate
          : events.find((item) => item.eventType === "ticket:comment:added")
              ?.metadata?.pathToAnnotate;
      const formattedFilters =
        rule.filters && rule.filters["~and"] ? rule.filters["~and"] : [];
      const filtersOr = [];
      (rule.considerations as string[]).forEach((consideration, idx) => {
        if (
          consideration === "nonWorkingHours" ||
          consideration === "groupUnavailableWorkingHours"
        ) {
          filtersOr.push({
            "~and": [
              {
                [`{{context.outputs.${idx + 1}.isAvailable}}`]: {
                  "~eq": false,
                },
              },
              {
                [`{{context.outputs.${idx + 1}.reason}}`]: {
                  "~eq": "OUTSIDE_BUSINESS_HOURS",
                },
              },
            ],
          });
        }
        if (
          consideration === "holidays" ||
          consideration === "groupUnavailableHolidays"
        ) {
          filtersOr.push({
            "~and": [
              {
                [`{{context.outputs.${idx + 1}.isAvailable}}`]: {
                  "~eq": false,
                },
              },
              {
                [`{{context.outputs.${idx + 1}.reason}}`]: { "~eq": "HOLIDAY" },
              },
            ],
          });
        }
        if (consideration === "memberUnavailableWorkingHours") {
          filtersOr.push({
            "~and": [
              {
                [`{{context.outputs.${idx + 1}.isAvailable}}`]: {
                  "~eq": false,
                },
              },
              {
                [`{{context.outputs.${idx + 1}.reason}}`]: {
                  "~eq": "OUTSIDE_BUSINESS_HOURS",
                },
              },
            ],
          });
        }
        if (consideration === "memberUnavailableTimeOff") {
          filtersOr.push({
            "~and": [
              {
                [`{{context.outputs.${idx + 1}.isAvailable}}`]: {
                  "~eq": false,
                },
              },
              {
                [`{{context.outputs.${idx + 1}.reason}}`]: {
                  "~eq": "TIME_OFF",
                },
              },
            ],
          });
        }
      });
      const payload = {
        name: rule.name,
        teamId: teamId,
        type: "MANUAL",
        subType: "AUTO_RESPONDER",
        triggerEvent:
          rule.trigger === "ticket_created"
            ? events.find((item) => item.eventType === "ticket:created")?.uid
            : events.find((item) => item.eventType === "ticket:comment:added")
                ?.uid,
        workflowDefinition: getWorkflowDefinition(pathToAnnotate, filtersOr),
        filters:
          rule.trigger === "ticket_created"
            ? {
                "~and": formattedFilters.map((f) => {
                  const field = Object.keys(f)[0];
                  const operator = Object.keys(f[field])[0];
                  const value = f[field][operator];
                  const operatorKey =
                    backendToLogical[operator] ||
                    (operator.startsWith("~") ? operator : `~${operator}`);
                  return {
                    [`{{${pathToAnnotate}.${field}}}`]: {
                      [operatorKey]: value,
                    },
                  };
                }),
              }
            : {
                "~and": [
                  {
                    "{{context.event.message.payload.comment.customerContact}}":
                      {
                        "~isempty": false,
                      },
                  },
                  ...formattedFilters.map((f) => {
                    const field = Object.keys(f)[0];
                    const operator = Object.keys(f[field])[0];
                    const value = f[field][operator];
                    const operatorKey =
                      backendToLogical[operator] ||
                      (operator.startsWith("~") ? operator : `~${operator}`);
                    return {
                      [`{{${pathToAnnotate}.${field}}}`]: {
                        [operatorKey]: value,
                      },
                    };
                  }),
                ],
              },
        metadata: {
          considerations: rule.considerations,
          filters: rule.filters["~and"] || [],
          message: rule.message,
          trigger: rule.trigger,
        },
      };
      if (isEdit) {
        handleUpdateWorkflow(payload);
      } else {
        handleCreateWorkflow(payload);
      }
    }
  };

  const hasNoConsiderations = (rule.considerations as string[]).length === 0;

  const handleBlur = (field: string) => {
    if (field === "name" || field === "message") {
      setTouched((prev) => ({
        ...prev,
        [field]: true,
      }));

      // Validate on blur
      if (field === "name" && !String(rule.name).trim()) {
        setErrors((prev) => ({
          ...prev,
          name: "Rule name is required.",
        }));
      }

      if (
        field === "message" &&
        !rule.message.content.trim() &&
        !rule.message.contentHtml.trim()
      ) {
        setErrors((prev) => ({
          ...prev,
          message: "Message is required.",
        }));
      }
    }
  };

  // Modify the fetch effect to include loading state
  useEffect(() => {
    if (!teamId) {
      toast.error("Team ID is required");
      return;
    }

    let isMounted = true;

    async function fetchData() {
      try {
        setIsLoadingData(true);
        const [eventsResponse, activitiesResponse] = await Promise.all([
          fetch(`/api/teams/${teamId}/workflows/registry/events`),
          fetch(`/api/teams/${teamId}/workflows/registry/activities`),
        ]);

        if (!isMounted) return;

        if (!eventsResponse.ok || !activitiesResponse.ok) {
          throw new Error("Failed to fetch workflow data");
        }

        const [eventsData, activitiesData] = await Promise.all([
          eventsResponse.json(),
          activitiesResponse.json(),
        ]);

        if (!isMounted) return;

        if (Array.isArray(eventsData.results)) {
          setEvents(eventsData.results);
        } else {
          console.error("Events data is not an array:", eventsData);
        }

        if (Array.isArray(activitiesData.results)) {
          setActivities(activitiesData.results);
        } else {
          console.error("Activities data is not an array:", activitiesData);
        }
      } catch (error) {
        console.error("Error fetching workflow data:", error);
        if (isMounted) {
          toast.error("Failed to load workflow data");
        }
      } finally {
        if (isMounted) {
          setIsLoadingData(false);
        }
      }
    }

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [teamId]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-auto sm:max-w-lg md:max-w-2xl w-[calc(100%-2rem)] p-4 sm:p-6">
        <DialogHeader>
          <DialogTitle className="text-xl">
            {isEdit ? "Edit rule" : "Create new rule"}
          </DialogTitle>
          <DialogDescription>
            Configure when and how out of office responses should be sent.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 my-4">
          {/* Rule Name */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="name" className="text-base font-medium">
                Rule name <span className="text-gray-500">*</span>
              </Label>
              {errors.name && touched.name && (
                <p className="text-sm text-gray-500">Rule name is required.</p>
              )}
            </div>
            <Input
              id="name"
              value={rule.name as string}
              onChange={(e) => handleChange("name", e.target.value)}
              onBlur={() => handleBlur("name")}
              className="h-10"
              style={{ borderRadius: "4px" }}
            />
          </div>

          {/* Trigger Section */}
          <Accordion
            type="single"
            collapsible
            defaultValue="trigger"
            className="w-full border rounded-md"
          >
            <AccordionItem value="trigger" className="border-none">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center flex-wrap gap-1">
                  <span className="text-base font-medium">
                    When should this rule trigger?
                  </span>
                  {errors.trigger && (
                    <p className="text-sm text-gray-500">
                      Trigger is required.
                    </p>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <Select
                    value={rule.trigger as string}
                    onValueChange={(value) => handleChange("trigger", value)}
                  >
                    <SelectTrigger
                      className="w-full h-auto py-3 px-4 rounded"
                      style={{ borderRadius: "4px" }}
                    >
                      <SelectValue placeholder="Select trigger">
                        {rule.trigger === "ticket_created" && (
                          <div className="flex flex-col items-start">
                            <span className="font-medium">Ticket created</span>
                            <span className="text-sm text-muted-foreground">
                              Respond when a new ticket is created.
                            </span>
                          </div>
                        )}
                        {rule.trigger === "message_received" && (
                          <div className="flex flex-col items-start">
                            <span className="font-medium">
                              Message received
                            </span>
                            <span className="text-sm text-muted-foreground">
                              Respond when a new message is received.
                            </span>
                          </div>
                        )}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ticket_created" className="py-2">
                        <div className="flex flex-col items-start gap-0.5">
                          <span className="font-medium">Ticket created</span>
                          <span className="text-sm text-muted-foreground">
                            Respond when a new ticket is created.
                          </span>
                        </div>
                      </SelectItem>
                      <SelectItem value="message_received" className="py-2">
                        <div className="flex flex-col items-start gap-0.5">
                          <span className="font-medium">Message received</span>
                          <span className="text-sm text-muted-foreground">
                            Respond when a new message is received.
                          </span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Considerations Section */}
          <Accordion
            type="single"
            collapsible
            defaultValue="considerations"
            className="w-full border rounded-md"
          >
            <AccordionItem value="considerations" className="border-none">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center flex-wrap gap-1">
                  <span className="text-base font-medium">
                    Apply this rule when:
                  </span>
                  {!hasNoConsiderations && (
                    <div className="flex flex-wrap gap-1 ml-2">
                      {(rule.considerations as string[]).map(
                        (consideration) => (
                          <Badge
                            key={consideration}
                            variant="outline"
                            className="bg-slate-100"
                          >
                            {consideration === "nonWorkingHours" &&
                              "Team non-working hours"}
                            {consideration === "holidays" && "Team holidays"}
                            {consideration === "memberUnavailableTimeOff" &&
                              "Member time off"}
                            {consideration ===
                              "memberUnavailableWorkingHours" &&
                              "Member non-working hours"}
                            {consideration === "groupUnavailableWorkingHours" &&
                              "Group non-working hours"}
                            {consideration === "groupUnavailableHolidays" &&
                              "Group holidays"}
                          </Badge>
                        ),
                      )}
                    </div>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <Select
                    value={undefined}
                    onValueChange={(value) => {
                      const current = rule.considerations as string[];
                      const updated = current.includes(value)
                        ? current.filter((c) => c !== value)
                        : [...current, value];
                      handleConsiderationChange(updated);
                    }}
                  >
                    <SelectTrigger
                      className="w-full h-auto py-3 px-4 rounded"
                      style={{ borderRadius: "4px" }}
                    >
                      <SelectValue placeholder="Select conditions">
                        <span className="text-sm text-muted-foreground">
                          {(rule.considerations as string[]).length === 0
                            ? "Select conditions..."
                            : `${
                                (rule.considerations as string[]).length
                              } condition${
                                (rule.considerations as string[]).length > 1
                                  ? "s"
                                  : ""
                              } selected`}
                        </span>
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      <div className="flex flex-col gap-2 p-2">
                        <div
                          className={`flex items-start gap-2 p-2 rounded cursor-pointer hover:bg-slate-100 ${
                            (rule.considerations as string[]).includes(
                              "nonWorkingHours",
                            )
                              ? "bg-slate-100"
                              : ""
                          }`}
                          onClick={() => {
                            const current = rule.considerations as string[];
                            const updated = current.includes("nonWorkingHours")
                              ? current.filter((c) => c !== "nonWorkingHours")
                              : [...current, "nonWorkingHours"];
                            handleConsiderationChange(updated);
                          }}
                        >
                          <div
                            className={`w-4 h-4 border rounded flex items-center justify-center mt-1 ${
                              (rule.considerations as string[]).includes(
                                "nonWorkingHours",
                              )
                                ? "bg-primary border-primary"
                                : "border-gray-300"
                            }`}
                          >
                            {(rule.considerations as string[]).includes(
                              "nonWorkingHours",
                            ) && (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="10"
                                height="10"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="3"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-white"
                              >
                                <polyline points="20 6 9 17 4 12"></polyline>
                              </svg>
                            )}
                          </div>
                          <div className="flex flex-col items-start gap-0.5">
                            <span className="font-medium">
                              Team non-working hours
                            </span>
                            <span className="text-sm text-muted-foreground">
                              Apply outside of team&apos;s working hours.
                            </span>
                          </div>
                        </div>

                        <div
                          className={`flex items-start gap-2 p-2 rounded cursor-pointer hover:bg-slate-100 ${
                            (rule.considerations as string[]).includes(
                              "holidays",
                            )
                              ? "bg-slate-100"
                              : ""
                          }`}
                          onClick={() => {
                            const current = rule.considerations as string[];
                            const updated = current.includes("holidays")
                              ? current.filter((c) => c !== "holidays")
                              : [...current, "holidays"];
                            handleConsiderationChange(updated);
                          }}
                        >
                          <div
                            className={`w-4 h-4 border rounded flex items-center justify-center mt-1 ${
                              (rule.considerations as string[]).includes(
                                "holidays",
                              )
                                ? "bg-primary border-primary"
                                : "border-gray-300"
                            }`}
                          >
                            {(rule.considerations as string[]).includes(
                              "holidays",
                            ) && (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="10"
                                height="10"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="3"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-white"
                              >
                                <polyline points="20 6 9 17 4 12"></polyline>
                              </svg>
                            )}
                          </div>
                          <div className="flex flex-col items-start gap-0.5">
                            <span className="font-medium">Team holidays</span>
                            <span className="text-sm text-muted-foreground">
                              Apply during configured holidays.
                            </span>
                          </div>
                        </div>

                        {rule.trigger !== "ticket_created" && (
                          <div
                            className={`flex items-start gap-2 p-2 rounded cursor-pointer hover:bg-slate-100 ${
                              (rule.considerations as string[]).includes(
                                "memberUnavailableWorkingHours",
                              )
                                ? "bg-slate-100"
                                : ""
                            }`}
                            onClick={() => {
                              const current = rule.considerations as string[];
                              const updated = current.includes(
                                "memberUnavailableWorkingHours",
                              )
                                ? current.filter(
                                    (c) =>
                                      c !== "memberUnavailableWorkingHours",
                                  )
                                : [...current, "memberUnavailableWorkingHours"];
                              handleConsiderationChange(updated);
                            }}
                          >
                            <div
                              className={`w-4 h-4 border rounded flex items-center justify-center mt-1 ${
                                (rule.considerations as string[]).includes(
                                  "memberUnavailableWorkingHours",
                                )
                                  ? "bg-primary border-primary"
                                  : "border-gray-300"
                              }`}
                            >
                              {(rule.considerations as string[]).includes(
                                "memberUnavailableWorkingHours",
                              ) && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="10"
                                  height="10"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-white"
                                >
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              )}
                            </div>
                            <div className="flex flex-col items-start gap-0.5">
                              <span className="font-medium">
                                Member non-working hours
                              </span>
                              <span className="text-sm text-muted-foreground">
                                Apply when assigned member is unavailable.
                              </span>
                            </div>
                          </div>
                        )}

                        {rule.trigger !== "ticket_created" && (
                          <div
                            className={`flex items-start gap-2 p-2 rounded cursor-pointer hover:bg-slate-100 ${
                              (rule.considerations as string[]).includes(
                                "memberUnavailableTimeOff",
                              )
                                ? "bg-slate-100"
                                : ""
                            }`}
                            onClick={() => {
                              const current = rule.considerations as string[];
                              const updated = current.includes(
                                "memberUnavailableTimeOff",
                              )
                                ? current.filter(
                                    (c) => c !== "memberUnavailableTimeOff",
                                  )
                                : [...current, "memberUnavailableTimeOff"];
                              handleConsiderationChange(updated);
                            }}
                          >
                            <div
                              className={`w-4 h-4 border rounded flex items-center justify-center mt-1 ${
                                (rule.considerations as string[]).includes(
                                  "memberUnavailableTimeOff",
                                )
                                  ? "bg-primary border-primary"
                                  : "border-gray-300"
                              }`}
                            >
                              {(rule.considerations as string[]).includes(
                                "memberUnavailableTimeOff",
                              ) && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="10"
                                  height="10"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-white"
                                >
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              )}
                            </div>
                            <div className="flex flex-col items-start gap-0.5">
                              <span className="font-medium">
                                Member time off
                              </span>
                              <span className="text-sm text-muted-foreground">
                                Apply when member is on time off.
                              </span>
                            </div>
                          </div>
                        )}

                        {rule.trigger !== "ticket_created" && (
                          <div
                            className={`flex items-start gap-2 p-2 rounded cursor-pointer hover:bg-slate-100 ${
                              (rule.considerations as string[]).includes(
                                "groupUnavailableWorkingHours",
                              )
                                ? "bg-slate-100"
                                : ""
                            }`}
                            onClick={() => {
                              const current = rule.considerations as string[];
                              const updated = current.includes(
                                "groupUnavailableWorkingHours",
                              )
                                ? current.filter(
                                    (c) => c !== "groupUnavailableWorkingHours",
                                  )
                                : [...current, "groupUnavailableWorkingHours"];
                              handleConsiderationChange(updated);
                            }}
                          >
                            <div
                              className={`w-4 h-4 border rounded flex items-center justify-center mt-1 ${
                                (rule.considerations as string[]).includes(
                                  "groupUnavailableWorkingHours",
                                )
                                  ? "bg-primary border-primary"
                                  : "border-gray-300"
                              }`}
                            >
                              {(rule.considerations as string[]).includes(
                                "groupUnavailableWorkingHours",
                              ) && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="10"
                                  height="10"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-white"
                                >
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              )}
                            </div>
                            <div className="flex flex-col items-start gap-0.5">
                              <span className="font-medium">
                                Group non-working hours
                              </span>
                              <span className="text-sm text-muted-foreground">
                                Apply outside of group&apos;s working hours.
                              </span>
                            </div>
                          </div>
                        )}

                        {rule.trigger !== "ticket_created" && (
                          <div
                            className={`flex items-start gap-2 p-2 rounded cursor-pointer hover:bg-slate-100 ${
                              (rule.considerations as string[]).includes(
                                "groupUnavailableHolidays",
                              )
                                ? "bg-slate-100"
                                : ""
                            }`}
                            onClick={() => {
                              const current = rule.considerations as string[];
                              const updated = current.includes(
                                "groupUnavailableHolidays",
                              )
                                ? current.filter(
                                    (c) => c !== "groupUnavailableHolidays",
                                  )
                                : [...current, "groupUnavailableHolidays"];
                              handleConsiderationChange(updated);
                            }}
                          >
                            <div
                              className={`w-4 h-4 border rounded flex items-center justify-center mt-1 ${
                                (rule.considerations as string[]).includes(
                                  "groupUnavailableHolidays",
                                )
                                  ? "bg-primary border-primary"
                                  : "border-gray-300"
                              }`}
                            >
                              {(rule.considerations as string[]).includes(
                                "groupUnavailableHolidays",
                              ) && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="10"
                                  height="10"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-white"
                                >
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              )}
                            </div>
                            <div className="flex flex-col items-start gap-0.5">
                              <span className="font-medium">
                                Group holidays
                              </span>
                              <span className="text-sm text-muted-foreground">
                                Apply during group&apos;s configured holidays.
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </SelectContent>
                  </Select>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Filters Section */}
          <Accordion
            type="single"
            collapsible
            className="w-full border rounded-md"
          >
            <AccordionItem value="filters" className="border-none">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center flex-wrap gap-1">
                  <span className="text-base font-medium">
                    Add filters (optional)
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <FilterBuilder
                  teamId={teamId}
                  filters={rule.filters["~and"] || []}
                  onAddFilter={(filter) => {
                    setRule((prev) => ({
                      ...prev,
                      filters: {
                        "~and": [
                          // Remove any filter with the same field (so you update, not duplicate)
                          ...(prev.filters["~and"] || []).filter(
                            (f) => Object.keys(f)[0] !== Object.keys(filter)[0],
                          ),
                          filter as FilterCondition,
                        ],
                      },
                    }));
                  }}
                  onRemoveFilter={(index) => {
                    setRule((prev) => {
                      // Get the current filters
                      const currentFilters = prev.filters["~and"] || [];

                      // Create a new array without the filter at the specified index
                      const updatedFilters = [
                        ...currentFilters.slice(0, index),
                        ...currentFilters.slice(index + 1),
                      ];

                      // Return the updated rule state
                      return {
                        ...prev,
                        filters: {
                          "~and": updatedFilters,
                        },
                      };
                    });
                  }}
                />
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Message Section */}
          <Accordion
            type="single"
            collapsible
            defaultValue="message"
            className={`w-full border rounded-md ${
              errors.message && touched.message ? "border-gray-300" : ""
            }`}
          >
            <AccordionItem value="message" className="border-none">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center flex-wrap gap-1">
                  <span className="text-base font-medium">
                    OOO-response message{" "}
                    <span className="text-gray-500">*</span>
                  </span>
                  {errors.message && touched.message && (
                    <p className="text-sm text-gray-500">
                      Message is required.
                    </p>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-2">
                  <RichTextEditor
                    value={rule.message.contentHtml}
                    onChange={(value) => handleChange("message", value)}
                    onBlur={() => handleBlur("message")}
                    error={errors.message && touched.message}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="h-10"
            style={{ borderRadius: "4px" }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="h-10"
            style={{ borderRadius: "4px" }}
            disabled={isLoadingData}
          >
            {isEdit ? "Save changes" : "Create rule"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
