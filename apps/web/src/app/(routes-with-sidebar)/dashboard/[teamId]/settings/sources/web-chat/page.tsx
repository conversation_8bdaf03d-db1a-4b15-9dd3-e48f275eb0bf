"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Co<PERSON>, Loader2, Plus, X } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { WebChatAgentBanner } from "../../../../../../../components/agents/web-chat-agent-banner";
import CommonSelectWrapper from "../../../../../../../components/common-select-wrapper";
import { Button } from "../../../../../../../components/ui/button";
import { Input } from "../../../../../../../components/ui/input";
import { AGENT_NAME } from "../../../../../../../constants/web-chat";
import { Agent } from "../../../../../../../types/agent";
import { ColorPicker } from "../../../../../helpcenter/[helpcenterId]/settings/_components/color-picker";
// Get Base URL and WS Endpoint from environment variables
const baseUrl = process.env.NEXT_PUBLIC_AGENT_STUDIO_URL;
const wsEndpoint = process.env.NEXT_PUBLIC_AGENT_STUDIO_WS_URL;
const widgetUrl = process.env.NEXT_PUBLIC_WIDGET_CDN_URL;

interface WidgetSettings {
  targetElementId?: string;
  useCustomLauncher?: boolean;
  initialPosition?: {
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  };
  themeColor?: string;
  brandLogoUrl?: string;
  userContextExample?: {
    email?: string;
    name?: string;
  };
}

interface WidgetConfig {
  baseUrl: string;
  apiKey: string;
  agentId: string;
  wsEndpoint: string;
  useCustomLauncher: boolean;
  user?: {
    email?: string;
    name?: string;
    hash?: string;
  };
  initialPosition?: {
    top: string;
    left: string;
    bottom: string;
    right: string;
  };
  targetElementId?: string;
  themeColor?: string;
  brandLogoUrl?: string;
  [key: string]: unknown;
}

export default function WebChatSettings() {
  const searchParams = useSearchParams();
  const agentId = searchParams.get("agentId");
  const [isLoadingDeployments, setIsLoadingDeployments] = useState(false);
  const [allowedDomains, setAllowedDomains] = useState<string[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);
  const [deploymentData, setDeploymentData] = useState(null);
  const [agentConfigurationPrompt, setAgentConfigurationPrompt] =
    useState<string>("");
  const [isSavingChatWidgetInstructions, setIsSavingChatWidgetInstructions] =
    useState(false);
  const queryClient = useQueryClient();

  // Widget specific settings states
  const [targetElementId, setTargetElementId] = useState<string>("");
  const [useCustomLauncher, setUseCustomLauncher] = useState<boolean>(false);
  const [initialPositionTop, setInitialPositionTop] = useState<string>("auto");
  const [initialPositionLeft, setInitialPositionLeft] =
    useState<string>("auto");
  const [initialPositionBottom, setInitialPositionBottom] =
    useState<string>("20px");
  const [initialPositionRight, setInitialPositionRight] =
    useState<string>("20px");
  const [selectedPositionType, setSelectedPositionType] =
    useState<string>("bottom-right");
  const [themeColor, setThemeColor] = useState<string>("");
  const [brandLogoUrl, setBrandLogoUrl] = useState<string>("");
  const [deploymentMode, setDeploymentMode] = useState<"floating" | "embedded">(
    "floating",
  );
  const [userEmail, setUserEmail] = useState<string>("<EMAIL>");
  const [userName, setUserName] = useState<string>("Test User (HMAC)");
  const { data: agent, isLoading } = useQuery<Agent>({
    queryKey: ["agent", agentId],
    queryFn: async () => {
      const response = await fetch(`/api/agents/${agentId}`);
      if (!response.ok) throw new Error("Failed to fetch agent");
      return response.json();
    },
    enabled: !!agentId,
  });
  useEffect(() => {
    async function fetchDeployments() {
      setIsLoadingDeployments(true);
      try {
        const response = await fetch(`/api/agents/${agentId}/deployments`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Failed to fetch deployments", errorData);
          setIsLoadingDeployments(false);
          return;
        }

        const deployments = await response.json();
        setDeploymentData(deployments[0]);
        // If we have deployments for this agent, use the first one
        if (deployments && deployments.length > 0) {
          const latestDeployment = deployments[0];
          // If the deployment has allowed origins, update state
          if (
            latestDeployment.allowed_origins &&
            latestDeployment.allowed_origins.length > 0
          ) {
            setAllowedDomains(latestDeployment.allowed_origins);
          }

          // Set selected team based on team_id if available
          if (latestDeployment.team_id) {
            setSelectedTeam(latestDeployment.team_id);
          }

          // Populate widget settings states from latestDeployment.widget_settings
          if (latestDeployment.widget_settings) {
            const settings = latestDeployment.widget_settings;
            setTargetElementId(settings.targetElementId || "my-thena-widget");
            setUseCustomLauncher(
              settings.useCustomLauncher === undefined
                ? false
                : settings.useCustomLauncher,
            );
            if (settings.initialPosition) {
              setInitialPositionTop(settings.initialPosition.top || "auto");
              setInitialPositionLeft(settings.initialPosition.left || "auto");
              setInitialPositionBottom(
                settings.initialPosition.bottom || "20px",
              );
              setInitialPositionRight(settings.initialPosition.right || "20px");
            }
            setThemeColor(settings.themeColor || "");
            setBrandLogoUrl(settings.brandLogoUrl || "");
            if (settings.userContextExample) {
              setUserEmail(
                settings.userContextExample.email || "<EMAIL>",
              );
              setUserName(
                settings.userContextExample.name || "Test User (HMAC)",
              );
            }
            // Determine deploymentMode based on targetElementId presence in fetched settings
            if (settings.targetElementId) {
              setDeploymentMode("embedded");
            } else {
              setDeploymentMode("floating");
            }
          }
        }
      } catch (err) {
        console.error("Error fetching deployments:", err);
      } finally {
        setIsLoadingDeployments(false);
      }
    }

    fetchDeployments();
  }, [agentId]);

  useEffect(() => {
    if (agent) {
      setAgentConfigurationPrompt(
        agent?.configuration?.chat_widget_instructions,
      );
    }
  }, [agent]);

  const generateWidgetCode = (apiKey: string, agentId: string) => {
    const thenaWidgetConfig: WidgetConfig = {
      baseUrl: baseUrl || "http://localhost:8008",
      apiKey: apiKey,
      agentId: agentId,
      wsEndpoint: wsEndpoint || "ws://localhost:8008",
      useCustomLauncher: useCustomLauncher, // Will be false if not configured
    };

    const user =
      userEmail || userName
        ? {
            ...(userEmail ? { email: userEmail } : {}),
            ...(userName ? { name: userName } : {}),
            hash: "YOUR_SERVER_SIDE_GENERATED_HMAC_HASH",
          }
        : undefined;

    if (user) {
      thenaWidgetConfig.user = user;
    }

    if (deploymentMode === "floating") {
      thenaWidgetConfig.initialPosition = {
        top: initialPositionTop,
        left: initialPositionLeft,
        bottom: initialPositionBottom,
        right: initialPositionRight,
      };
      // Defaults are handled by the widget itself, so only include if not default/empty
      // thenaWidgetConfig.dimensions = { width: dimensionsWidth, height: dimensionsHeight }; // Removed as per previous request
      // thenaWidgetConfig.animationStyle = animationStyle; // Removed as per previous request
    } else if (deploymentMode === "embedded") {
      if (targetElementId) {
        thenaWidgetConfig.targetElementId = targetElementId;
      }
    }

    if (themeColor) {
      thenaWidgetConfig.themeColor = themeColor;
    }
    if (brandLogoUrl) {
      thenaWidgetConfig.brandLogoUrl = brandLogoUrl;
    }

    // Function to convert the object to a nicely formatted string for the script tag
    const configToString = (obj: Record<string, unknown>, indent = "  ") => {
      let result = "{\n";
      const keys = Object.keys(obj);
      keys.forEach((key, index) => {
        const value = obj[key];
        result += `${indent}  ${key}: `;
        if (typeof value === "string") {
          result += `'${value.replace(/'/g, "\\'")}'`;
        } else if (typeof value === "boolean" || typeof value === "number") {
          result += value;
        } else if (typeof value === "object" && value !== null) {
          result += configToString(
            value as Record<string, unknown>,
            `${indent}  `,
          ); // Recursive call for nested objects
        } else if (value === undefined) {
          // Skip undefined values
          result = result.substring(
            0,
            result.lastIndexOf(`${indent}  ${key}: `),
          ); // Remove the key line
          return; // and skip adding comma
        } else {
          result += "null";
        }
        if (
          index < keys.length - 1 &&
          !(
            value === undefined &&
            keys.slice(index + 1).every((k) => obj[k] === undefined)
          )
        ) {
          // Add comma if not the last real key
          const nextRealKeyIndex = keys.findIndex(
            (k, i) => i > index && obj[k] !== undefined,
          );
          if (nextRealKeyIndex !== -1) {
            result += ",\n";
          }
        } else if (value !== undefined) {
          result += "\n";
        }
      });
      result += `${indent.substring(0, indent.length - 2)}}`;
      return result;
    };

    const configString = configToString(thenaWidgetConfig);

    return `<script>\n  window.thenaWidget = ${configString};\n</script>\n<script src="${(
      widgetUrl || "https://widget.thena.tools/shim.js"
    ).replace(/'/g, "\\'")}"></script>`;
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        console.log(`${type} copied to clipboard.`);
      },
      (err) => {
        console.error(`Could not copy ${type}: ${err}`);
      },
    );
  };

  const handleDeploy = async () => {
    try {
      const widgetSettingsForApi: WidgetSettings = {
        useCustomLauncher: useCustomLauncher,
        themeColor: themeColor || undefined,
        brandLogoUrl: brandLogoUrl || undefined,
        userContextExample: {
          email: userEmail || undefined,
          name: userName || undefined,
        },
      };

      if (deploymentMode === "floating") {
        widgetSettingsForApi.initialPosition = {
          top: initialPositionTop,
          left: initialPositionLeft,
          bottom: initialPositionBottom,
          right: initialPositionRight,
        };
      } else if (deploymentMode === "embedded") {
        widgetSettingsForApi.targetElementId = targetElementId;
      }
      // Remove userContextExample if both fields are empty
      if (
        !widgetSettingsForApi.userContextExample.email &&
        !widgetSettingsForApi.userContextExample.name
      ) {
        delete widgetSettingsForApi.userContextExample;
      }

      const response = await fetch(`/api/agents/${agent.id}/deployments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          team_id: selectedTeam,
          allowed_origins: allowedDomains,
          deployment_type: "widget",
          widget_settings: widgetSettingsForApi,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Deployment failed");
      }

      toast.success(
        "Deployment successful! Your widget configuration is ready.",
      );
      setDeploymentData(result);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred.";
      toast.error(errorMessage);
    }
  };
  const handleDomainChange = (index: number, value: string) => {
    const newDomains = [...allowedDomains];
    newDomains[index] = value;
    setAllowedDomains(newDomains);
  };

  const addDomain = () => {
    setAllowedDomains([...allowedDomains, ""]);
  };

  const removeDomain = (index: number) => {
    setAllowedDomains(allowedDomains.filter((_, i) => i !== index));
  };

  const updateAgentConfigMutation = useMutation({
    mutationFn: async (newInstructions: string) => {
      if (!agent?.id) throw new Error("Agent data not available for update.");
      setIsSavingChatWidgetInstructions(true);
      const updatedConfiguration = {
        ...agent.configuration,
        chat_widget_instructions: newInstructions,
      };

      const response = await fetch(`/api/agents/${agent.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: agent.name,
          status: agent.status,
          configuration: updatedConfiguration,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Failed to update agent configuration",
        );
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success("Chat widget instructions saved successfully.");
      queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
      setIsSavingChatWidgetInstructions(false);
    },
    onError: (error: Error) => {
      toast.error(`Error saving instructions: ${error.message}`);
      setAgentConfigurationPrompt(
        agent.configuration?.chat_widget_instructions || "",
      );
      setIsSavingChatWidgetInstructions(false);
    },
  });

  return (
    <div className="h-[calc(100vh-14rem)] flex flex-col">
      <div className="px-6 pt-8 h-full">
        <div className="grid gap-4 mx-auto max-w-[640px]">
          <div>
            <h2 className="text-2xl font-medium">Web chat</h2>
            <p className="text-sm text-[var(--color-text-muted)]">
              Embed support chat directly on your website.
            </p>
          </div>
          <div className="border-border border border-solid rounded-sm w-full p-4">
            <h3 className="text-lg font-medium mb-6">Set customization</h3>
            <label className="text-sm text-color-text">Logo</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Add your company logo using a public URL (SVG, PNG, JPG, JPEG,
              WebP supported).
            </p>
            <Input
              id="brand-logo-url"
              type="url"
              value={brandLogoUrl}
              className="flex-grow mb-4"
              onChange={(e) => setBrandLogoUrl(e.target.value)}
              placeholder="https://example.com/logo.svg"
              disabled={isLoading || isLoadingDeployments}
            />

            <label className="text-sm text-color-text">Theme color</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Pick a color to match your brand. This will be used across the
              chat widget and interface.
            </p>
            <div className="flex gap-2 mb-4">
              <div className="flex gap-2 items-center border rounded-sm px-2 py-1">
                <ColorPicker
                  color={themeColor}
                  setColor={(color) => setThemeColor(color)}
                />
              </div>
              <div className="flex gap-2 items-center border rounded-sm px-2 py-1 w-28">
                <span className="w-18 inline-block grow-0 shrink-0">
                  {themeColor}
                </span>
              </div>
            </div>

            <label className="text-sm text-color-text">Position</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Choose where the chat widget should appear on your site.
            </p>

            <div className="mb-4">
              <CommonSelectWrapper
                placeholder="Select position"
                hideClearIndicator
                isClearable={false}
                options={[
                  { value: "bottom-right", label: "Bottom right" },
                  { value: "bottom-left", label: "Bottom left" },
                  { value: "top-right", label: "Top right" },
                  { value: "top-left", label: "Top left" },
                  { value: "custom", label: "Custom position" },
                ]}
                name="position"
                value={{
                  value: selectedPositionType,
                  label:
                    selectedPositionType === "bottom-right"
                      ? "Bottom right"
                      : selectedPositionType === "bottom-left"
                      ? "Bottom left"
                      : selectedPositionType === "top-right"
                      ? "Top right"
                      : selectedPositionType === "top-left"
                      ? "Top left"
                      : "Custom position",
                }}
                onChange={(value) => {
                  const position = value as { value: string; label: string };
                  setSelectedPositionType(position.value);
                  switch (position.value) {
                    case "bottom-right":
                      setInitialPositionTop("auto");
                      setInitialPositionLeft("auto");
                      setInitialPositionBottom("20px");
                      setInitialPositionRight("20px");
                      break;
                    case "bottom-left":
                      setInitialPositionTop("auto");
                      setInitialPositionRight("auto");
                      setInitialPositionBottom("20px");
                      setInitialPositionLeft("20px");
                      break;
                    case "top-right":
                      setInitialPositionBottom("auto");
                      setInitialPositionLeft("auto");
                      setInitialPositionTop("20px");
                      setInitialPositionRight("20px");
                      break;
                    case "top-left":
                      setInitialPositionBottom("auto");
                      setInitialPositionRight("auto");
                      setInitialPositionTop("20px");
                      setInitialPositionLeft("20px");
                      break;
                    case "custom":
                      // Do not change values, allow user to edit
                      break;
                  }
                }}
                isVirtualized={false}
                isOptionsMemoized
                triggerClassname="w-full h-6"
                wrapperClassname="!max-w-[80%] w-full"
              />
            </div>

            {selectedPositionType === "custom" && (
              <div className="grid grid-cols-4 gap-4 mb-4">
                <div>
                  <label className="text-sm text-color-text mb-2 block">
                    Left
                  </label>
                  <Input
                    type="text"
                    value={
                      initialPositionLeft === "auto" ? "" : initialPositionLeft
                    }
                    onChange={(e) =>
                      setInitialPositionLeft(
                        e.target.value.trim() === "" ? "auto" : e.target.value,
                      )
                    }
                    placeholder="px"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-sm text-color-text mb-2 block">
                    Right
                  </label>
                  <Input
                    type="text"
                    value={
                      initialPositionRight === "auto"
                        ? ""
                        : initialPositionRight
                    }
                    onChange={(e) =>
                      setInitialPositionRight(
                        e.target.value.trim() === "" ? "auto" : e.target.value,
                      )
                    }
                    placeholder="px"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-sm text-color-text mb-2 block">
                    Top
                  </label>
                  <Input
                    type="text"
                    value={
                      initialPositionTop === "auto" ? "" : initialPositionTop
                    }
                    onChange={(e) =>
                      setInitialPositionTop(
                        e.target.value.trim() === "" ? "auto" : e.target.value,
                      )
                    }
                    placeholder="px"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-sm text-color-text mb-2 block">
                    Bottom
                  </label>
                  <Input
                    type="text"
                    value={
                      initialPositionBottom === "auto"
                        ? ""
                        : initialPositionBottom
                    }
                    onChange={(e) =>
                      setInitialPositionBottom(
                        e.target.value.trim() === "" ? "auto" : e.target.value,
                      )
                    }
                    placeholder="px"
                    className="w-full"
                  />
                </div>
              </div>
            )}

            <label className="text-sm text-color-text">Widget type</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Select the default chat experience. Use Thena&apos;s standard
              widget or link it to a custom element on your site.
            </p>
            <div className="mb-4">
              <CommonSelectWrapper
                placeholder="Select widget type"
                hideClearIndicator
                isClearable={false}
                options={[
                  { value: "floating", label: "Thena's default widget" },
                  { value: "embedded", label: "Custom embed" },
                ]}
                name="widget-type"
                value={{
                  value: deploymentMode,
                  label:
                    deploymentMode === "floating"
                      ? "Thena's default widget"
                      : "Custom embed",
                }}
                onChange={(value) => {
                  const option = value as { value: string; label: string };
                  setDeploymentMode(option.value as "floating" | "embedded");
                }}
                isVirtualized={false}
                isOptionsMemoized
                triggerClassname="w-full h-6"
                wrapperClassname="!max-w-[80%] w-full"
              />
            </div>
            {deploymentMode === "embedded" && (
              <div className="mb-4">
                <label className="text-sm text-color-text flex items-center gap-1">
                  Target element ID
                  <span
                    className="ml-1 text-xs text-[var(--color-text-muted)] cursor-pointer"
                    tabIndex={0}
                    aria-label="Specify the ID element where the chat widget should be embedded."
                  >
                    &#9432;
                  </span>
                </label>
                <p className="text-sm text-[var(--color-text-muted)] mb-2">
                  Specify the ID element where the chat widget should be
                  embedded.
                </p>
                <Input
                  type="text"
                  value={targetElementId}
                  onChange={(e) => setTargetElementId(e.target.value)}
                  placeholder="e.g., my-thena-widget"
                  className="w-full"
                  aria-label="Target element ID"
                />
              </div>
            )}

            <div className="w-full flex justify-end">
              <Button
                onClick={handleDeploy}
                disabled={isLoading || isLoadingDeployments}
                size="sm"
                className="mt-3"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save
              </Button>
            </div>
          </div>

          <div className="border-border border border-solid rounded-sm w-full p-4">
            <h3 className="text-lg font-medium mb-6">Deploy web chat</h3>
            <label className="text-sm text-color-text">Code snippet</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Add this code to your website&apos;s HTML, just before the closing{" "}
              {`</body>`} tag. Customize the appearance and behavior using the
              configuration options.
            </p>
            <div className="py-4">
              <div className="space-y-4">
                <div className="relative">
                  <pre className="p-4 rounded-sm bg-muted font-mono text-sm overflow-x-auto">
                    {generateWidgetCode("YOUR_API_KEY", agent?.id)}
                  </pre>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute top-2 right-2"
                    onClick={() =>
                      copyToClipboard(
                        generateWidgetCode("YOUR_API_KEY", agent?.id),
                        "Widget Code",
                      )
                    }
                  >
                    <Copy className="h-4 w-4" />
                    <span className="sr-only">Copy code</span>
                  </Button>
                </div>
              </div>
            </div>
            <label className="text-sm text-color-text">HMAC secret key</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              This key is shown ONLY ONCE. Store it securely on your server and
              never expose it client-side.
            </p>

            <div className="relative flex items-center mb-6">
              <Input
                id="hmac-key-display"
                readOnly
                value={deploymentData?.hmac_secret_key || ""}
                className="font-mono text-xs pr-10 bg-background"
              />
              {deploymentData?.hmac_secret_key !==
                "Secret key only shown during creation" && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute right-1 h-7 w-7 p-0"
                  onClick={() =>
                    copyToClipboard(
                      deploymentData?.hmac_secret_key,
                      "HMAC Secret Key",
                    )
                  }
                >
                  <Copy className="h-3.5 w-3.5" />
                  <span className="sr-only">Copy HMAC Secret Key</span>
                </Button>
              )}
            </div>

            <label className="text-sm text-color-text">Allowed domains</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Enter the full domains (including http/https) where your widget
              will be hosted. This helps secure your deployment..
            </p>

            <div className="space-y-3">
              {allowedDomains.map((domain, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    type="text"
                    value={domain}
                    onChange={(e) => handleDomainChange(index, e.target.value)}
                    placeholder="https://www.example.com"
                    className="flex-grow"
                    disabled={isLoading || isLoadingDeployments}
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeDomain(index)}
                    aria-label="Remove domain"
                    disabled={isLoading || isLoadingDeployments}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={addDomain}
                className="mt-3"
                disabled={isLoading || isLoadingDeployments}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add domain
              </Button>
            </div>
            <div className="w-full flex justify-end">
              <Button
                onClick={handleDeploy}
                disabled={isLoading || isLoadingDeployments}
                size="sm"
                className="mt-3"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save
              </Button>
            </div>
          </div>

          <div className="border-border border border-solid rounded-sm w-full p-4">
            <div className="flex-1 min-w-0 text-left">
              <div className="flex flex-col gap-4">
                <div className="flex items-center">
                  <h3 className="text-base font-medium truncate flex items-center">
                    Agent configuration prompt
                  </h3>
                </div>

                <textarea
                  id="agent-configuration-prompt"
                  className="w-full p-3 text-sm border border-border rounded-sm resize-y min-h-[100px] bg-background focus:ring-1 focus:ring-primary/30 focus:border-primary/30 outline-none transition-colors"
                  value={agentConfigurationPrompt}
                  placeholder={`Set ${agent?.name}'s support style and handoff rules. Define tone, response format, and when to escalate to your team.`}
                  onChange={(e) => {
                    setAgentConfigurationPrompt(e.target.value);
                  }}
                />
                <div className="flex justify-end">
                  <Button
                    onClick={() =>
                      updateAgentConfigMutation.mutate(agentConfigurationPrompt)
                    }
                    disabled={isSavingChatWidgetInstructions}
                  >
                    {isSavingChatWidgetInstructions && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Save
                  </Button>
                </div>
              </div>
            </div>
          </div>
          {agent && (
            <WebChatAgentBanner AGENT_NAME={AGENT_NAME} agentDetails={agent} />
          )}
        </div>
      </div>
    </div>
  );
}
