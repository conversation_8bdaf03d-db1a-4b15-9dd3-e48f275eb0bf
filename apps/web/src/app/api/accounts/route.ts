import { BASE_URL } from "@/config/constant";
import { createClient } from "@/lib/supabase-server";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

interface ApiResponse<T> {
  data?: T;
  error?: string;
}

interface CustomFieldValue {
  uid: string;
  value: string | number | boolean | string[];
  fieldType:
    | "short_text"
    | "long_text"
    | "rich_text"
    | "dropdown"
    | "checkbox"
    | "multiselect"
    | "number"
    | "date"
    | "email"
    | "url"
    | "phone";
}

interface Contact {
  id: string;
  name: string;
  email: string;
  avatarUrl: string | null;
}

interface Account {
  id: string;
  name: string;
  description: string;
  source: string;
  logo?: string;
  statusId: string;
  status: string;
  primaryDomain: string;
  secondaryDomain?: string | null;
  accountOwner: string;
  accountOwnerId: string;
  accountOwnerEmail: string;
  annualRevenue?: number;
  employees?: number;
  website?: string;
  billingAddress?: string;
  shippingAddress?: string;
  createdAt: string;
  updatedAt: string;
  customFieldValues?: CustomFieldValue[];
  contacts?: Contact[];
}

export async function GET(request: Request) {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value) {
      return NextResponse.json(
        { error: "Unauthorized: No session token found" },
        { status: 401 },
      );
    }

    if (!orgUid) {
      return NextResponse.json(
        { error: "Unauthorized: No organization ID found" },
        { status: 401 },
      );
    }

    // Get pagination parameters from URL
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "1000", 10);
    const page = parseInt(searchParams.get("page") || "1", 10);
    const offset = (page - 1) * limit;

    // Get search query parameter
    const searchQuery = searchParams.get("search") || "";

    // Initialize Supabase client
    const supabase = await createClient();

    // Get the organization ID from the database using the orgUid
    const { data: organizationData, error: orgError } = await supabase
      .from("organization")
      .select("id")
      .eq("uid", orgUid)
      .single();

    if (orgError) {
      console.error("Error fetching organization:", orgError);
      return NextResponse.json(
        { error: "Failed to identify organization" },
        { status: 500 },
      );
    }

    if (!organizationData) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 },
      );
    }

    // Query accounts from Supabase with joined attribute values
    let query = supabase
      .from("accounts")
      .select(
        `
        id,
        uid,
        name,
        description,
        is_active,
        logo,
        source,
        primary_domain,
        secondary_domain,
        annual_revenue,
        employees,
        website,
        billing_address,
        shipping_address,
        account_owner_id,
        created_at,
        updated_at,
        deleted_at,
        status,
        classification,
        health,
        industry,
        account_owner:account_owner_id(id, uid, name, email, avatar_url),
        status_value:status(id, value),
        classification_value:classification(id, value),
        health_value:health(id, value),
        industry_value:industry(id, value)
      `,
        { count: "exact" },
      )
      .eq("organization_id", organizationData.id)
      .is("deleted_at", null);

    // Add search query if provided
    if (searchQuery) {
      // First, get account owner IDs that match the search query
      const { data: matchingOwners } = await supabase
        .from("users")
        .select("id")
        .ilike("name", `%${searchQuery}%`);

      const ownerIds = matchingOwners?.map((owner) => owner.id) || [];

      // Build the search query
      let searchFilter = supabase
        .from("accounts")
        .select("id")
        .eq("organization_id", organizationData.id)
        .is("deleted_at", null)
        .or(
          `name.ilike.%${searchQuery}%,primary_domain.ilike.%${searchQuery}%`,
        );

      // Add account owner filter if we found matching owners
      if (ownerIds.length > 0) {
        searchFilter = searchFilter.or(
          `account_owner_id.in.(${ownerIds.join(",")})`,
        );
      }

      // Get IDs of accounts that match the search criteria
      const { data: matchingAccounts } = await searchFilter;

      if (matchingAccounts && matchingAccounts.length > 0) {
        const accountIds = matchingAccounts.map((account) => account.id);
        // Filter the main query to only include matching accounts
        query = query.in("id", accountIds);
      } else {
        // No matches found, return empty result
        query = query.eq("id", -1); // This will return no results
      }
    }

    // Apply sorting and pagination
    const {
      data: accounts,
      error,
      count: totalCount,
    } = await query
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error("Supabase query error:", error);
      return NextResponse.json(
        { error: `API Error: ${error.message}` },
        { status: 500 },
      );
    }

    // Fetch custom field values for all accounts
    const accountIds = accounts?.map((account) => account.id) || [];
    const customFieldValuesMap: Record<
      number,
      Array<{
        customFieldId: string;
        data: Array<{ value: string }>;
        metadata: unknown;
      }>
    > = {};

    if (accountIds.length > 0) {
      // First, get the account_custom_field_values join table entries
      const { data: accountCustomFieldJoins, error: customFieldJoinError } =
        await supabase
          .from("account_custom_field_values")
          .select("account_id, custom_field_value_id")
          .in("account_id", accountIds);

      if (customFieldJoinError) {
        console.error(
          "Error fetching account custom field joins:",
          customFieldJoinError,
        );
      } else if (
        accountCustomFieldJoins &&
        accountCustomFieldJoins.length > 0
      ) {
        // Get all the custom field value IDs
        const customFieldValueIds = accountCustomFieldJoins.map(
          (join) => join.custom_field_value_id,
        );

        // Fetch the actual custom field values
        const { data: customFieldValues, error: customFieldValuesError } =
          await supabase
            .from("custom_field_values")
            .select(
              "id, data, metadata, custom_field_id, custom_field:custom_field_id(uid)",
            )
            .in("id", customFieldValueIds);

        if (customFieldValuesError) {
          console.error(
            "Error fetching custom field values:",
            customFieldValuesError,
          );
        } else if (customFieldValues) {
          // Create a map of custom field values by ID
          const customFieldValuesById: Record<
            number,
            {
              id: number;
              data: Array<{ value: string }>;
              metadata: unknown;
              custom_field_id: number;
              custom_field: Array<{ uid: string }> | { uid: string } | null;
            }
          > = {};

          customFieldValues.forEach((cfv) => {
            customFieldValuesById[cfv.id] = cfv;
          });

          // Group custom field values by account ID
          accountCustomFieldJoins.forEach((join) => {
            const accountId = join.account_id;
            const customFieldValue =
              customFieldValuesById[join.custom_field_value_id];

            if (customFieldValue) {
              if (!customFieldValuesMap[accountId]) {
                customFieldValuesMap[accountId] = [];
              }

              // Format the custom field value according to the expected structure
              const formattedValue = {
                customFieldId: Array.isArray(customFieldValue.custom_field)
                  ? customFieldValue.custom_field[0]?.uid || ""
                  : customFieldValue.custom_field?.uid || "",
                data: customFieldValue.data || [],
                metadata: customFieldValue.metadata,
              };

              customFieldValuesMap[accountId].push(formattedValue);
            }
          });
        }
      }
    }

    // Fetch contacts for all accounts
    const contactsByAccountId: Record<number, Contact[]> = {};

    if (accountIds.length > 0) {
      // Get contacts associated with accounts through the junction table
      const { data: accountContacts, error: accountContactsError } =
        await supabase
          .from("customer_contact_accounts")
          .select("account_id, customer_contact_id")
          .in("account_id", accountIds);

      if (accountContactsError) {
        console.error("Error fetching account contacts:", accountContactsError);
      } else if (accountContacts && accountContacts.length > 0) {
        // Get all the contact IDs
        const contactIds = accountContacts.map(
          (join) => join.customer_contact_id,
        );

        // Fetch the actual contacts
        const { data: contacts, error: contactsError } = await supabase
          .from("customer_contacts")
          .select("id, uid, first_name, last_name, email, avatar_url")
          .in("id", contactIds)
          .is("deleted_at", null);

        if (contactsError) {
          console.error("Error fetching contacts:", contactsError);
        } else if (contacts) {
          // Create a map of contacts by ID
          const contactsById: Record<
            number,
            {
              id: number;
              uid: string;
              first_name: string;
              last_name: string;
              email: string;
              avatar_url: string | null;
            }
          > = {};

          contacts.forEach((contact) => {
            contactsById[contact.id] = contact;
          });

          // Group contacts by account ID
          accountContacts.forEach((join) => {
            const accountId = join.account_id;
            const contact = contactsById[join.customer_contact_id];

            if (contact) {
              if (!contactsByAccountId[accountId]) {
                contactsByAccountId[accountId] = [];
              }

              // Format the contact according to the expected structure
              const formattedContact = {
                id: contact.uid,
                name: `${contact.first_name} ${contact.last_name || ""}`.trim(),
                email: contact.email,
                avatarUrl: contact.avatar_url,
              };

              contactsByAccountId[accountId].push(formattedContact);
            }
          });
        }
      }
    }

    // Transform Supabase data to match the expected format
    const transformedAccounts =
      accounts?.map((account) => {
        // Handle potentially null nested objects
        const accountOwnerObj = Array.isArray(account.account_owner)
          ? account.account_owner[0]
          : account.account_owner;

        const statusObj = Array.isArray(account.status_value)
          ? account.status_value[0]
          : account.status_value;

        const classificationObj = Array.isArray(account.classification_value)
          ? account.classification_value[0]
          : account.classification_value;

        const healthObj = Array.isArray(account.health_value)
          ? account.health_value[0]
          : account.health_value;

        const industryObj = Array.isArray(account.industry_value)
          ? account.industry_value[0]
          : account.industry_value;

        // Check if name contains special characters
        const hasSpecialChars = /[^\w\s-]/.test(account.name);
        if (hasSpecialChars) {
          return null; // Skip this account
        }

        return {
          id: account.uid,
          name: account.name.trim(),
          description: account.description || "",
          source: account.source || "",
          logo: account.logo,
          statusId: account.status?.toString() || "",
          status: statusObj?.value || "",
          classification: classificationObj?.value || "",
          classificationId: account.classification?.toString() || "",
          health: healthObj?.value || "",
          healthId: account.health?.toString() || "",
          industry: industryObj?.value || "",
          industryId: account.industry?.toString() || "",
          primaryDomain: account.primary_domain || "",
          secondaryDomain: account.secondary_domain,
          accountOwner: accountOwnerObj?.name || "",
          accountOwnerId: accountOwnerObj?.uid || "",
          accountOwnerAvatarUrl: accountOwnerObj?.avatar_url || "",
          accountOwnerEmail: accountOwnerObj?.email || "",
          annualRevenue: account.annual_revenue,
          employees: account.employees,
          website: account.website,
          billingAddress: account.billing_address,
          shippingAddress: account.shipping_address,
          createdAt: account.created_at,
          updatedAt: account.updated_at,
          // Include custom field values for this account
          customFieldValues: customFieldValuesMap[account.id] || [],
          // Include contacts for this account
          customerContacts: contactsByAccountId[account.id] || [],
        };
      })
      .filter(Boolean) || []; // Remove null entries

    // Calculate pagination using the total count from the database
    const totalPages = Math.ceil((totalCount || 0) / limit);

    return NextResponse.json({
      accounts: transformedAccounts,
      pagination: {
        limit,
        page,
        hasMore: page < totalPages,
        total: totalCount || 0,
      },
    });
  } catch (error) {
    console.error("Error fetching accounts:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(
  request: Request,
): Promise<NextResponse<ApiResponse<Account>>> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");
    if (!session?.value || !orgUid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Trim the fields
    body.name = body.name?.trim();
    body.primaryDomain = body.primaryDomain?.trim();
    body.description = body.description?.trim();

    // Check for special characters in name
    if (body.name && !/^[a-zA-Z0-9\s\-_]+$/.test(body.name)) {
      return NextResponse.json(
        { error: "Name can only contain letters, numbers, spaces, hyphens, and underscores" },
        { status: 400 }
      );
    }

    // Validate and normalize domain
    if (body.primaryDomain) {
      let normalizedDomain = body.primaryDomain;
      
      // Extract hostname if URL is provided
      try {
        if (normalizedDomain.startsWith('http://') || normalizedDomain.startsWith('https://')) {
          normalizedDomain = new URL(normalizedDomain).hostname;
        }
      } catch (_e) {
        return NextResponse.json(
          { error: "Please enter a valid domain (e.g., example.com)" },
          { status: 400 }
        );
      }
      
      // Validate domain format
      const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$/;
      if (!domainRegex.test(normalizedDomain)) {
        return NextResponse.json(
          { error: "Please enter a valid domain (e.g., example.com)" },
          { status: 400 }
        );
      }
      
      // Update the body with normalized domain
      body.primaryDomain = normalizedDomain;
    }

    const response = await fetch(`${BASE_URL}/v1/accounts`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${session.value}`,
        "Content-Type": "application/json",
        "x-org-id": orgUid,
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error("Failed to create account");
    }

    const data = await response.json();
    return NextResponse.json({ data });
  } catch (error) {
    console.error("Error creating account:", error);
    return NextResponse.json(
      { error: "Failed to create account" },
      { status: 500 },
    );
  }
}
