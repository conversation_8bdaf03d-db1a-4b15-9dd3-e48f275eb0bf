"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface CreateAccountDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  refetch?: () => Promise<void>;
}

export function CreateAccountDialog({
  open,
  onOpenChange,
  refetch,
}: CreateAccountDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    primaryDomain: "",
    description: "",
  });

  // Validation functions
  const validateName = (name: string): boolean => {
    // Only allow word characters, spaces, and hyphens
    const nameRegex = /^[a-zA-Z0-9\s\-_]+$/;
    return nameRegex.test(name);
  };

  const validateDomain = (domain: string): boolean => {
    // Remove http:// or https:// if present
    const cleanDomain = domain.replace(/^https?:\/\//, '');
    // More permissive domain regex that handles edge cases better
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$/;
    return domainRegex.test(cleanDomain) && cleanDomain.length <= 255;
  };

  // Reset form data when dialog opens or closes
  useEffect(() => {
    setFormData({
      name: "",
      primaryDomain: "",
      description: "",
    });
  }, [open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate name
      if (!formData.name.trim() || !validateName(formData.name.trim())) {
        toast.error(!formData.name.trim() ? "Account name is required" : "Account name can only contain letters, numbers, spaces, and basic punctuation (-_)");
        setIsLoading(false);
        return;
      }

      // Validate domain
      if (!formData.primaryDomain.trim() || !validateDomain(formData.primaryDomain.trim())) {
        toast.error(!formData.primaryDomain.trim() ? "Primary domain is required" : "Please enter a valid domain name");
        setIsLoading(false);
        return;
      }

      const response = await fetch("/api/accounts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          source: "Thena",
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create account");
      }

      toast.success("Account created successfully");
      onOpenChange(false);

      if (refetch) {
        await refetch();
      } else {
        window.location.reload();
      }
    } catch (error) {
      console.error("Error creating account:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create account",
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create new account</DialogTitle>
            <DialogDescription>
              Add a new account to your organization.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">
                Account name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                required
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="primaryDomain">
                Primary domain <span className="text-red-500">*</span>
              </Label>
              <Input
                id="primaryDomain"
                required
                value={formData.primaryDomain}
                onChange={(e) =>
                  setFormData({ ...formData, primaryDomain: e.target.value })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Creating..." : "Create account"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
