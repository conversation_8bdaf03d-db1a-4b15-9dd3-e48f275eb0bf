import React from "react";

interface WebChatIconProps {
  className?: string;
  size?: number;
}

const WebChatIcon: React.FC<WebChatIconProps> = ({
  className = "",
  size = 16,
}) => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 288 312"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_1046_237)">
        <path
          d="M76.3948 145.921C76.3948 161.658 67.8899 175.738 55.8953 175.738C67.8899 175.738 76.3948 189.819 76.3948 205.556C76.3948 189.819 84.8998 175.738 96.8944 175.738C84.8998 175.738 76.3948 161.658 76.3948 145.921Z"
          fill="url(#paint0_linear_1046_237)"
        />
        <path
          d="M100.622 175.738C100.622 183.608 96.3709 190.647 90.3716 190.647C96.3709 190.647 100.622 197.688 100.622 205.556C100.622 197.688 104.873 190.647 110.872 190.647C104.873 190.647 100.622 183.608 100.622 175.738Z"
          fill="url(#paint1_linear_1046_237)"
        />
        <path
          d="M105.281 145.921C105.281 153.79 101.03 160.829 95.0308 160.829C101.029 160.829 105.281 167.87 105.281 175.738C105.281 167.87 109.532 160.829 115.531 160.829C109.532 160.829 105.281 153.79 105.281 145.921Z"
          fill="url(#paint2_linear_1046_237)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M231.992 264.553C231.992 205.089 186.836 156.436 128.509 143.822L128.509 264.553L231.992 264.553Z"
          fill="url(#paint3_linear_1046_237)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M162.377 46.5464L162.377 133.041L241.4 133.041L263.978 133.041C263.978 87.9915 245.163 46.5464 162.377 46.5464Z"
          fill="url(#paint4_linear_1046_237)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M126.628 133.04L126.628 46.5464L47.605 46.5464L23.1455 46.5464C23.1455 91.5954 41.9605 133.04 126.628 133.04Z"
          fill="url(#paint5_linear_1046_237)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_1046_237"
          x1="76.3948"
          y1="145.921"
          x2="76.3948"
          y2="205.556"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6805FF" />
          <stop offset="1" stop-color="#11B0FE" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_1046_237"
          x1="100.622"
          y1="175.738"
          x2="100.622"
          y2="205.556"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6805FF" />
          <stop offset="1" stop-color="#11B0FE" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_1046_237"
          x1="105.281"
          y1="145.921"
          x2="105.281"
          y2="175.738"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6805FF" />
          <stop offset="1" stop-color="#11B0FE" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_1046_237"
          x1="180.251"
          y1="143.822"
          x2="180.251"
          y2="264.553"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6A00FF" />
          <stop offset="1" stop-color="#008CFF" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_1046_237"
          x1="213.178"
          y1="46.5464"
          x2="213.178"
          y2="133.04"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6A00FF" />
          <stop offset="1" stop-color="#008CFF" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_1046_237"
          x1="74.8868"
          y1="46.5464"
          x2="74.8868"
          y2="133.04"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6A00FF" />
          <stop offset="1" stop-color="#008CFF" />
        </linearGradient>
        <clipPath id="clip0_1046_237">
          <rect
            width="268.05"
            height="265.564"
            fill="white"
            transform="matrix(0.984808 -0.173648 -0.0871557 0.996195 23.1455 46.5464)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default WebChatIcon;
