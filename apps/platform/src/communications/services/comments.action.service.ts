import { InjectQueue } from "@nestjs/bullmq";
import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import {
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  Comment,
  CommentEntityTypes,
  CommentProcessorService,
  CommentRepository,
  CommentType,
  CommentVisibility,
  CustomerContact,
  MentionExtractor,
  Reactions,
  ReactionsRepository,
  Storage,
  TransactionContext,
  TransactionService,
  User,
  UserType,
} from "@repo/thena-platform-entities";
import { Queue } from "bullmq";
import * as rTracer from "cls-rtracer";
import { cloneDeep, isArray, mergeWith } from "lodash";
import { DeepPartial, In, IsNull, Less<PERSON>han, More<PERSON>han, Not } from "typeorm";
import { CustomerContactActionService } from "../../accounts/services/customer-contact.action.service";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators";
import { QueueNames } from "../../constants/queue.constants";
import { StorageService } from "../../storage/services/storage-service";
import { UsersService } from "../../users/services/users.service";
import { EAGERLY_LOAD_COMMENTS_RELATIONS } from "../constants/comments.constants";
import {
  CreateComment,
  GetCommentByUserTypeQuery,
  GetCommentQuery,
  GetCommentThreadsQuery,
  HasCustomerCommentQuery,
  HasInternalMemberCommentQuery,
} from "../dto/comment.queries";
import { UpdateCommentDto } from "../dto/comments.dto";
import { CommentOp } from "../utils";

@Injectable()
export class CommentsActionService {
  constructor(
    private readonly transactionService: TransactionService,
    private readonly commentProcessorService: CommentProcessorService,
    private readonly activitiesService: ActivitiesService,
    private readonly usersService: UsersService,
    private readonly customerContactActionService: CustomerContactActionService,
    private readonly storageService: StorageService,

    // Repositories
    private readonly reactionsRepository: ReactionsRepository,
    private readonly commentRepository: CommentRepository,

    @InjectQueue(QueueNames.COMMENT_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,
  ) {}

  /**
   * Get a comment by its unique identifier and eagerly load the relations
   * @param commentId - The unique identifier of the comment
   * @param orgId - The unique identifier of the organization
   * @returns The comment with the relations eagerly loaded
   */
  async getPopulatedCommentById(
    commentId: string,
    orgId: string,
    txnContext?: TransactionContext,
  ) {
    if (txnContext) {
      return await this.commentRepository.findByConditionWithTxn(txnContext, {
        where: { uid: commentId, organizationId: orgId },
        relations: EAGERLY_LOAD_COMMENTS_RELATIONS,
      });
    }

    return await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: orgId },
      relations: EAGERLY_LOAD_COMMENTS_RELATIONS,
    });
  }

  /**
   * Get the allowed visibilities for a comment based on the user type
   * @param currentUser The currently logged in user
   * @param teamId The unique identifier of the team
   * @param organizationId The unique identifier of the organization
   * @returns The allowed visibilities
   */
  private getAllowedVisibilities(
    currentUser: CurrentUser,
    _organizationId: string,
    _teamId: string,
    accountId: string,
  ) {
    if (accountId) {
      // Account notes are always internal comments
      return [CommentVisibility.PRIVATE];
    }

    // If the user is a customer user, only get public comments
    switch (currentUser.userType) {
      // Customer users can only see public comments
      case UserType.CUSTOMER_USER: {
        return [CommentVisibility.PUBLIC];
      }

      // Internal users can see all comments
      case UserType.ORG_ADMIN:
      case UserType.BOT_USER:
      case UserType.USER: {
        // If the user is not in the team, they can only see public and internal comments
        return [CommentVisibility.PUBLIC];
      }

      default: {
        throw new UnauthorizedException("Invalid user type!");
      }
    }
  }

  /**
   * Create a new comment
   * @param comment The comment data
   * @returns The newly created comment
   */
  async createComment(comment: CreateComment, txnContext?: TransactionContext) {
    const newComment = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Extract the user and customer IDs from the comment content
        const { userIds } = MentionExtractor.extractMentions(
          comment.contentHtml ?? comment.content,
        );

        const { plainText, html, markdown } =
          this.commentProcessorService.processComment(comment.content);

        const commentData: DeepPartial<Comment> = {
          organizationId: comment.organizationId,
          content: plainText,
          contentHtml: comment.contentHtml ?? html,
          contentJson: comment.contentJson ?? null,
          contentMarkdown: markdown,
          commentVisibility:
            comment.commentVisibility ?? CommentVisibility.PUBLIC,
          commentType: comment.commentType ?? CommentType.COMMENT,
          parentCommentId: comment.parentCommentId,
          metadata: {
            mentions: userIds,
            source: comment.source,
            ...comment.metadata,
          },
          authorId: comment.authorId,
          ticketId: comment.ticketId ?? null,
          teamId: comment.teamId ?? null,
          accountNoteId: comment.accountNoteId ?? null,
          accountActivityId: comment.accountActivityId ?? null,
          accountTaskId: comment.accountTaskId ?? null,
          accountId: comment.accountId ?? null,
          commentThreadName: comment.commentThreadName ?? null,
          impersonatedUserEmail: comment.impersonatedUserEmail ?? null,
          impersonatedUserName: comment.impersonatedUserName ?? null,
          impersonatedUserAvatar: comment.impersonatedUserAvatar ?? null,
        };

        // If the comment is associated with a customer contact, set the customer contact
        if (comment.customerId) {
          commentData.customerContact = { id: comment.customerId };
        }

        // Create the comment
        const createdComment = await this.commentRepository.createComment(
          commentData,
          txnContext,
        );

        // Create audit logs for comment
        const auditLog: DeepPartial<AuditLog> = {
          ...(createdComment.teamId && { team: { id: createdComment.teamId } }),
          organization: { id: createdComment.organizationId },
          activityPerformedBy: { id: createdComment.authorId },
          entityId: createdComment.id,
          entityUid: createdComment.uid,
          entityType: AuditLogEntityType.COMMENTS,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.SYSTEM,
          activity: `A new comment ${createdComment.id} was created`,
          description: `A new comment ${createdComment.id} was created by ${createdComment.authorId} `,
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Update the parent comment's metadata
        if (comment.parentCommentId) {
          const parentId = comment.parentCommentId;
          const orgId = comment.organizationId;
          const parentCommentCriteria = { id: parentId, organizationId: orgId };

          // Update the parent comment's metadata
          await this.commentRepository.updateWithTxn(
            txnContext,
            parentCommentCriteria,
            {
              metadata: () => `
              jsonb_set(
                COALESCE(metadata, '{"replies": []}'::jsonb),
                '{replies}',
                COALESCE(metadata->'replies', '[]'::jsonb) || '["${createdComment.uid}"]'::jsonb
              )`,
            },
          );
        }

        return createdComment;
      },
      txnContext,
    );

    // Fetch the comment with populated relations
    const populatedComment = await this.getPopulatedCommentById(
      newComment.uid,
      comment.organizationId,
      txnContext,
    );

    return populatedComment;
  }

  /**
   * Deep merge metadata objects, preserving nested structures
   */
  private deepMergeMetadata(target: any, source: any): any {
    if (!source || typeof source !== "object") return target;
    if (!target || typeof target !== "object") return source;

    const result = { ...target };

    for (const key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        if (
          target[key] &&
          typeof target[key] === "object" &&
          typeof source[key] === "object" &&
          !Array.isArray(target[key]) &&
          !Array.isArray(source[key])
        ) {
          // Recursively merge nested objects
          result[key] = this.deepMergeMetadata(target[key], source[key]);
        } else {
          // Overwrite with source value
          result[key] = source[key];
        }
      }
    }

    return result;
  }

  /**
   * Update a comment
   * @param commentId The unique identifier of the comment
   * @param updateCommentDto The update comment data
   * @returns The updated comment
   */
  async updateComment(
    commentId: string,
    updateCommentDto: UpdateCommentDto,
    currentUser: CurrentUser,
  ): Promise<Comment> {
    // Check if the comment exists
    const comment = await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: currentUser.orgId },
      relations: [
        "team",
        "author",
        "ticket",
        "attachments",
        "accountNote",
        "accountActivity",
        "accountTask",
        "customerContact",
      ],
    });

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException("Comment not found!");
    if (comment.deletedAt)
      throw new BadRequestException(
        "This comment was deleted and therefore can no longer be edited/updated!",
      );

    // Check if the current user is a bot user
    const isBotUser = currentUser.userType === UserType.BOT_USER;
    const isAuthorSetAs = updateCommentDto.commentAs === comment.author?.email;
    const isCustomerContactSetAs =
      updateCommentDto.commentAs === comment.customerContact?.email;
    const canBotUserUpdate =
      isBotUser && (isAuthorSetAs || isCustomerContactSetAs);

    // Is user's comment comment
    const isUserComment = comment.authorId === currentUser.sub;

    // If the current user is not the author of the comment, throw an error
    if (!isUserComment && !canBotUserUpdate) {
      throw new UnauthorizedException(
        "You are not authorized to update this comment!",
      );
    }

    // Run the update transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Process the updated content
      const { content, metadata: newMetadata } = updateCommentDto;

      // Debug logging
      console.log(
        "🔍 [DEBUG] UpdateCommentDto received:",
        JSON.stringify(updateCommentDto, null, 2),
      );
      console.log("🔍 [DEBUG] Content value:", JSON.stringify(content));
      console.log("🔍 [DEBUG] Content type:", typeof content);
      console.log("🔍 [DEBUG] Metadata value:", JSON.stringify(newMetadata));

      // Update criteria
      const updateCriteria = {
        id: comment.id,
        organizationId: comment.organizationId,
      };

      // Check if this update should skip content changes (strict enforcement)
      // If the comment already has the skipSlackContentUpdate flag, always respect it
      const shouldSkipContentUpdate =
        newMetadata?.skipSlackContentUpdate ||
        (comment.metadata as any)?.skipSlackContentUpdate;

      console.log(
        "🔍 [DEBUG] shouldSkipContentUpdate:",
        shouldSkipContentUpdate,
      );

      // If this update should skip content changes, force it to be metadata-only FIRST
      if (shouldSkipContentUpdate && content) {
        console.log(
          "🔍 [FORCE METADATA ONLY] Preventing content update due to skipSlackContentUpdate flag",
        );
        // Force this to be a metadata-only update by clearing the content
        updateCommentDto.content = undefined;
        updateCommentDto.contentHtml = undefined;
        updateCommentDto.contentJson = undefined;
      }

      // NOW check if content is being updated (after potential clearing)
      const isContentUpdate =
        updateCommentDto.content && updateCommentDto.content.trim().length > 0;

      // Check if this is a Slack metadata-only update (to prevent content overwrites)
      const isSlackMetadataUpdate =
        newMetadata && newMetadata.external_sinks?.slack && !isContentUpdate;

      console.log("🔍 [DEBUG] isContentUpdate:", isContentUpdate);
      console.log("🔍 [DEBUG] isSlackMetadataUpdate:", isSlackMetadataUpdate);

      if (isContentUpdate) {
        // Extract the user IDs from the updated content
        // Always use contentHtml for extraction if available, as it preserves the mention structure
        const { userIds } = MentionExtractor.extractMentions(
          updateCommentDto.contentHtml || content,
        );

        // Process the content, but don't override the contentHtml
        const { plainText, html, markdown } =
          this.commentProcessorService.processComment(content);

        // Merge metadata at application level if needed
        let finalMetadata: any = comment.metadata || {};

        // Update mentions when content changes
        finalMetadata.mentions = userIds;

        console.log(
          "🔍 [CONTENT UPDATE] Original metadata:",
          JSON.stringify(finalMetadata, null, 2),
        );
        if (newMetadata) {
          console.log(
            "🔍 [CONTENT UPDATE] New metadata to merge:",
            JSON.stringify(newMetadata, null, 2),
          );
          finalMetadata = this.deepMergeMetadata(finalMetadata, newMetadata);
          console.log(
            "🔍 [CONTENT UPDATE] Merged metadata:",
            JSON.stringify(finalMetadata, null, 2),
          );
        }

        // Set lastEditedAt and lastEditedBy
        finalMetadata.lastEditedAt = new Date().toISOString();
        finalMetadata.lastEditedBy = currentUser.sub;

        // Update the comment
        await this.commentRepository.updateWithTxn(txnContext, updateCriteria, {
          content: plainText,
          contentJson: updateCommentDto.contentJson ?? null,
          // Always use the provided contentHtml if available, don't override with processed HTML
          contentHtml: updateCommentDto.contentHtml ?? html,
          contentMarkdown: markdown,
          isEdited: true,
          commentThreadName: updateCommentDto.threadName ?? null,
          metadata: finalMetadata,
        });
      } else if (newMetadata) {
        console.log(
          "🔍 [METADATA ONLY] Original metadata:",
          JSON.stringify(comment.metadata || {}, null, 2),
        );
        console.log(
          "🔍 [METADATA ONLY] New metadata to merge:",
          JSON.stringify(newMetadata, null, 2),
        );

        // Merge metadata at application level to avoid SQL complexity
        const mergedMetadata = this.deepMergeMetadata(
          comment.metadata || {},
          newMetadata,
        );

        console.log(
          "🔍 [METADATA ONLY] Merged metadata:",
          JSON.stringify(mergedMetadata, null, 2),
        );

        // For Slack metadata updates, add a flag to prevent content overwrites
        if (isSlackMetadataUpdate) {
          // Add a flag to tell the Slack app not to update content
          mergedMetadata.skipSlackContentUpdate = true;
          console.log("🔍 [SLACK METADATA] Added skipSlackContentUpdate flag");
        } else {
          mergedMetadata.lastEditedAt = new Date().toISOString();
        }

        // Use updateWithTxn instead of createQueryBuilder for consistency
        await this.commentRepository.updateWithTxn(txnContext, updateCriteria, {
          // Only set isEdited for non-Slack metadata updates to avoid triggering unnecessary events
          isEdited: !isSlackMetadataUpdate,
          commentThreadName: updateCommentDto.threadName ?? null,
          metadata: mergedMetadata,
        });
      }

      if (updateCommentDto.attachments !== undefined) {
        const attachments =
          updateCommentDto.attachments.length === 0
            ? []
            : await this.storageService.attachFilesToEntity(
                updateCommentDto.attachments,
                comment.organizationId,
              );

        await txnContext.manager
          .getRepository(Comment)
          .createQueryBuilder()
          .relation("attachments")
          .of(comment)
          .addAndRemove(attachments, comment.attachments || []);
      }

      // Only update these fields if they haven't been updated already
      const fieldsToUpdate: any = {
        isEdited: true,
      };

      // Only add fields that are being updated and weren't handled above
      if (
        updateCommentDto.threadName !== undefined &&
        !content &&
        !newMetadata
      ) {
        fieldsToUpdate.commentThreadName = updateCommentDto.threadName;
      }
      if (updateCommentDto.commentVisibility !== undefined) {
        fieldsToUpdate.commentVisibility = updateCommentDto.commentVisibility;
      }
      if (updateCommentDto.commentType !== undefined) {
        fieldsToUpdate.commentType = updateCommentDto.commentType;
      }
      if (updateCommentDto.isPinned !== undefined) {
        fieldsToUpdate.isPinned = updateCommentDto.isPinned;
      }

      // Only perform this update if there are fields to update beyond isEdited
      if (Object.keys(fieldsToUpdate).length > 1) {
        await this.commentRepository.updateWithTxn(
          txnContext,
          updateCriteria,
          fieldsToUpdate,
        );
      }
    });

    // Fetch the comment with populated relations
    const populatedComment = await this.getPopulatedCommentById(
      comment.uid,
      comment.organizationId,
    );

    return populatedComment;
  }

  /**
   * Deletes a comment.
   * @param commentId The unique identifier of the comment
   * @param currentUser The currently logged in user
   */
  async deleteComment(
    commentId: string,
    currentUser: CurrentUser,
    metadata?: Record<string, any>,
  ) {
    // Check if the comment exists with full relations
    const comment = await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: currentUser.orgId },
      relations: [
        "team",
        "author",
        "ticket",
        "attachments",
        "accountNote",
        "accountActivity",
        "accountTask",
        "parentComment",
        "customerContact",
      ],
    });

    const fullCommentData = cloneDeep(comment);

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException("Comment not found!");

    // If the current user is not the author of the comment, throw an error
    if (comment.authorId !== currentUser.sub) {
      throw new UnauthorizedException(
        "You are not authorized to delete this comment!",
      );
    }

    // Run the delete transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      if (metadata) {
        const updatedMetadata = mergeWith(
          fullCommentData.metadata,
          metadata,
          (a, b) => {
            if (isArray(a)) {
              return a.concat(b);
            }
          },
        );

        await this.commentRepository.updateWithTxn(
          txnContext,
          {
            id: comment.id,
            organizationId: currentUser.orgId,
          },
          {
            metadata: updatedMetadata,
          },
        );
      }

      // Delete the comment
      await this.commentRepository.softDeleteWithTxn(txnContext, {
        id: comment.id,
        uid: comment.uid,
        organizationId: currentUser.orgId,
      });

      fullCommentData.deletedAt = new Date();

      // Add to SNS publish queue
      await this.snsPublishQueue.add(
        QueueNames.COMMENT_SNS_PUBLISHER,
        {
          comment: comment.uid,
          user: currentUser,
          eventType: CommentOp.DELETED,
          entityType: CommentEntityTypes.TICKET,
          previousComment: fullCommentData,
          reqId: rTracer.id(),
        },
        {
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 1000,
          },
        },
      );
    });
  }

  /**
   * Get comments for a ticket
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The comments and the total number of comments
   */
  async getComments(
    getCommentQuery: GetCommentQuery & {
      teamId?: string;
      ticketId?: string;
      accountId?: string;
      accountNoteId?: string;
      accountActivityId?: string;
      accountTaskId?: string;
    },
    currentUser: CurrentUser,
  ) {
    const {
      page,
      limit,
      teamId,
      ticketId,
      accountId,
      accountNoteId,
      accountActivityId,
      accountTaskId,
    } = getCommentQuery;

    // Get the allowed visibilities
    const allowedVisibilities = this.getAllowedVisibilities(
      currentUser,
      currentUser.orgId,
      teamId,
      accountId,
    );

    const comments = await this.commentRepository.fetchPaginatedResults(
      { page, limit },
      {
        where: [
          // Get all the comments including private and public comments for the user
          // in the team
          {
            ...(ticketId && { ticketId }),
            ...(teamId && { teamId }),
            ...(accountId && { accountId }),
            ...(accountNoteId && { accountNoteId }),
            ...(accountActivityId && { accountActivityId }),
            ...(accountTaskId && { accountTaskId }),
            organizationId: currentUser.orgId,
            commentType: CommentType.COMMENT,
            commentVisibility: In([
              ...allowedVisibilities,
              CommentVisibility.PRIVATE,
            ]),
            parentCommentId: IsNull(),
          },

          // Only fetch public notes for the user in the team
          {
            ...(ticketId && { ticketId }),
            ...(teamId && { teamId }),
            ...(accountId && { accountId }),
            ...(accountNoteId && { accountNoteId }),
            ...(accountActivityId && { accountActivityId }),
            ...(accountTaskId && { accountTaskId }),
            organizationId: currentUser.orgId,
            commentType: CommentType.NOTE,
            commentVisibility: CommentVisibility.PUBLIC,
            parentCommentId: IsNull(),
          },

          // Get all private comments for the current user
          {
            ...(ticketId && { ticketId }),
            ...(teamId && { teamId }),
            ...(accountId && { accountId }),
            ...(accountNoteId && { accountNoteId }),
            ...(accountActivityId && { accountActivityId }),
            ...(accountTaskId && { accountTaskId }),
            organizationId: currentUser.orgId,
            commentType: CommentType.NOTE,
            commentVisibility: CommentVisibility.PRIVATE,
            parentCommentId: IsNull(),
            author: { id: currentUser.sub },
          },
        ],
        relations: ["author", "attachments", "customerContact"],
      },
    );

    // Enhance the comments with user reactions
    const enhancedComments = await this.enhanceCommentsWithUserReactions(
      comments.results,
      currentUser,
    );

    return {
      results: enhancedComments,
      total: comments.total,
    };
  }

  /**
   * Get comments for a ticket
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The comments and the total number of comments
   */
  async getAllComments(
    entityProperties: {
      ticketIds?: string[];
      accountNoteIds?: string[];
      accountActivityIds?: string[];
      accountTaskIds?: string[];
    },
    currentUser: CurrentUser,
  ) {
    const { ticketIds, accountNoteIds, accountActivityIds, accountTaskIds } =
      entityProperties;

    const comments = await this.commentRepository.findAll({
      where: [
        // Get all the comments including private and public comments for the user
        {
          ...(ticketIds?.length > 0 && { ticketId: In(ticketIds) }),
          ...(accountNoteIds?.length > 0 && {
            accountNoteId: In(accountNoteIds),
          }),
          ...(accountActivityIds?.length > 0 && {
            accountActivityId: In(accountActivityIds),
          }),
          ...(accountTaskIds?.length > 0 && {
            accountTaskId: In(accountTaskIds),
          }),
          organizationId: currentUser.orgId,
          commentType: CommentType.COMMENT,
        },

        // Only fetch public notes for the user in the team
        {
          ...(ticketIds?.length > 0 && { ticketId: In(ticketIds) }),
          ...(accountNoteIds?.length > 0 && {
            accountNoteId: In(accountNoteIds),
          }),
          ...(accountActivityIds?.length > 0 && {
            accountActivityId: In(accountActivityIds),
          }),
          ...(accountTaskIds?.length > 0 && {
            accountTaskId: In(accountTaskIds),
          }),
          organizationId: currentUser.orgId,
          commentType: CommentType.NOTE,
          commentVisibility: CommentVisibility.PUBLIC,
        },

        // Get all private comments for the current user
        {
          ...(ticketIds?.length > 0 && { ticketId: In(ticketIds) }),
          ...(accountNoteIds?.length > 0 && {
            accountNoteId: In(accountNoteIds),
          }),
          ...(accountActivityIds?.length > 0 && {
            accountActivityId: In(accountActivityIds),
          }),
          ...(accountTaskIds?.length > 0 && {
            accountTaskId: In(accountTaskIds),
          }),
          organizationId: currentUser.orgId,
          commentType: CommentType.NOTE,
          commentVisibility: CommentVisibility.PRIVATE,
          author: { id: currentUser.sub },
        },
      ],
      relations: [
        "team",
        "author",
        "ticket",
        "attachments",
        "accountNote",
        "accountActivity",
        "accountTask",
        "parentComment",
        "customerContact",
      ],
    });

    // Enhance the comments with user reactions
    const enhancedComments = await this.enhanceCommentsWithUserReactions(
      comments,
      currentUser,
    );

    return {
      results: enhancedComments,
    };
  }

  /**
   * Get comments for a ticket
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The comments and the total number of comments
   */
  async getCommentByUserType(
    getCommentByUserTypeQuery: GetCommentByUserTypeQuery & {
      organizationId: string;
      teamId?: string;
      ticketId?: string;
      accountId?: string;
      accountNoteId?: string;
      accountActivityId?: string;
      accountTaskId?: string;
    },
    currentUser: CurrentUser,
  ) {
    const {
      teamId,
      ticketId,
      organizationId,
      userType,
      accountId,
      accountNoteId,
      accountActivityId,
      accountTaskId,
      firstComment,
    } = getCommentByUserTypeQuery;

    // Get the allowed visibilities
    const allowedVisibilities = this.getAllowedVisibilities(
      currentUser,
      organizationId,
      teamId,
      accountId,
    );
    const comments = await this.commentRepository.findWithRelations({
      where: {
        ...(ticketId && { ticketId }),
        ...(teamId && { teamId }),
        ...(accountId && { accountId }),
        ...(accountNoteId && { accountNoteId }),
        ...(accountActivityId && { accountActivityId }),
        ...(accountTaskId && { accountTaskId }),
        organizationId,
        commentVisibility: In(allowedVisibilities),
        commentType: CommentType.COMMENT,
        ...(userType === "customer" && {
          customerContact: Not(IsNull()),
        }),
        ...(userType === "agent" && {
          customerContact: IsNull(),
        }),
      },
      relations: ["author", "team", "customerContact"],
      order: firstComment ? { id: "ASC" } : { id: "DESC" },
      take: 1,
    });

    // Since we're expecting only one result, return the first item or null
    return comments[0] || null;
  }

  async hasCustomerComment(
    query: HasCustomerCommentQuery & {
      organizationId: string;
      teamId?: string;
      ticketId?: string;
      accountId?: string;
      accountNoteId?: string;
      accountActivityId?: string;
      accountTaskId?: string;
    },
  ): Promise<boolean> {
    const {
      organizationId,
      teamId,
      ticketId,
      accountId,
      accountNoteId,
      accountActivityId,
      accountTaskId,
      since,
      parentCommentId,
    } = query;

    const hasCustomerComment = await this.commentRepository.exists({
      where: {
        ...(ticketId && { ticketId }),
        ...(teamId && { teamId }),
        ...(accountId && { accountId }),
        ...(accountNoteId && { accountNoteId }),
        ...(accountActivityId && { accountActivityId }),
        ...(accountTaskId && { accountTaskId }),
        organizationId,
        commentType: CommentType.COMMENT,
        commentVisibility: CommentVisibility.PUBLIC,
        ...(parentCommentId && { parentCommentId }),
        customerContact: Not(IsNull()),
        ...(since && { createdAt: MoreThan(new Date(since)) }),
      },
    });

    return hasCustomerComment;
  }

  async hasInternalMemberComment(
    query: HasInternalMemberCommentQuery & {
      organizationId: string;
      teamId?: string;
      ticketId?: string;
      accountId?: string;
      accountNoteId?: string;
      accountActivityId?: string;
      accountTaskId?: string;
    },
  ): Promise<boolean> {
    const {
      organizationId,
      teamId,
      ticketId,
      accountId,
      accountNoteId,
      accountActivityId,
      accountTaskId,
      since,
      parentCommentId,
      commentType,
      commentVisibility,
    } = query;

    const hasInternalMemberComment = await this.commentRepository.exists({
      where: {
        ...(ticketId && { ticketId }),
        ...(teamId && { teamId }),
        ...(accountId && { accountId }),
        ...(accountNoteId && { accountNoteId }),
        ...(accountActivityId && { accountActivityId }),
        ...(accountTaskId && { accountTaskId }),
        organizationId,
        customerContact: IsNull(),
        ...(commentType && { commentType }),
        ...(commentVisibility && { commentVisibility }),
        ...(parentCommentId && { parentCommentId }),
        ...(since && { createdAt: MoreThan(new Date(since)) }),
      },
    });

    return hasInternalMemberComment;
  }

  /**
   * Get the threads for a comment
   * @param commentId The unique identifier of the comment
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The threads for the comment
   */
  async getCommentThreads(
    commentId: string,
    getCommentQuery: GetCommentThreadsQuery,
    currentUser: CurrentUser,
  ) {
    const { page, limit } = getCommentQuery;

    // Check if the comment exists
    const comment = await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: currentUser.orgId },
    });

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException("Comment not found!");

    // Get the allowed visibilities
    const allowedVisibilities = this.getAllowedVisibilities(
      currentUser,
      currentUser.orgId,
      comment.teamId,
      comment.accountId,
    );

    // Get all the visibilities
    const allVisibilities = [...allowedVisibilities, comment.commentVisibility];

    // Get the thread comments
    const threadComments = await this.commentRepository.fetchPaginatedResults(
      { page: page ?? 0, limit: Math.max(limit ?? 10, 100) },
      {
        where: [
          {
            organizationId: currentUser.orgId,
            commentVisibility: In(allVisibilities),
            parentCommentId: comment.id,
          },
          {
            organizationId: currentUser.orgId,
            commentVisibility: CommentVisibility.PRIVATE,
            parentCommentId: comment.id,
            author: { id: currentUser.sub },
          },
        ],
        relations: { attachments: true },
        order: { createdAt: "ASC" },
      },
    );

    // Create a set of unique values for the authors and customer contacts
    const authorSet = new Set<string>();
    const customerContactSet = new Set<string>();

    for (const comment of threadComments.results) {
      authorSet.add(comment.authorId);
      customerContactSet.add(comment.customerContactId);
    }

    const [authors, customerContacts] = await Promise.all([
      this.usersService.queryUsers({ id: In(Array.from(authorSet)) }),
      this.customerContactActionService.queryCustomerContacts({
        id: In(Array.from(customerContactSet)),
      }),
    ]);

    const authorMap = new Map<string, User>();
    const customerContactMap = new Map<string, CustomerContact>();

    for (const author of authors) {
      authorMap.set(author.id, author);
    }

    for (const customerContact of customerContacts) {
      customerContactMap.set(customerContact.id, customerContact);
    }

    // Join the comments with the authors and customer contacts
    const joinedComments = threadComments.results.map((comment) => {
      return {
        ...comment,
        author: authorMap.get(comment.authorId),
        customerContact: customerContactMap.get(comment.customerContactId),
      };
    }) as Array<Comment & { author: User; customerContact: CustomerContact }>;

    // Enhance the thread comments with user reactions
    const enhancedThreadComments = await this.enhanceCommentsWithUserReactions(
      joinedComments,
      currentUser,
    );

    return {
      results: enhancedThreadComments,
      total: threadComments.total,
    };
  }

  async updateCommentAttachments(comment: Comment, attachments: Storage[]) {
    comment.attachments = attachments;
    await this.commentRepository.save(comment);
  }

  /**
   * Enhance comments with user reactions
   * @param comments The comments to enhance
   * @param currentUser The currently logged in user
   * @returns The comments with user reactions
   */
  async enhanceCommentsWithUserReactions(
    comments: Comment[],
    currentUser: CurrentUser,
  ) {
    if (!comments.length) {
      return comments;
    }

    // Get all comment ids
    const commentIds = comments.map((comment) => comment.id);

    // Get reactions for the above comments by the current user
    const userReactions = await this.reactionsRepository.findAll({
      where: {
        commentId: In(commentIds),
        reactionBy: { id: currentUser.sub },
        organization: { id: currentUser.orgId },
      },
      relations: { emoji: true },
    });

    // Map for a faster lookup
    const reactionMap = new Map<string, Reactions[]>();
    userReactions.forEach((reaction) => {
      // Initialize the array for the comment id if it doesn't exist
      if (!reactionMap.has(reaction.commentId)) {
        reactionMap.set(reaction.commentId, []);
      }

      // Add the reaction to the map
      reactionMap.get(reaction.commentId)?.push(reaction);
    });

    return comments.map((comment) => {
      const userReactionsForComment = reactionMap.get(comment.id) || [];

      // Initialize the metadata if it doesn't exist
      if (!comment.metadata) {
        comment.metadata = {
          reactions: {},
          replies: [],
          mentions: [],
          source: comment?.metadata?.source,
          ignoreSelf: false,
          integrationMetadata: [],
          external_metadata: {},
          external_sinks: {},
        };
      }

      // Add the user reactions to the metadata
      comment.metadata.userReactions = userReactionsForComment.map(
        (reaction) => ({
          emojiId: reaction.emojiId,
          emojiName: reaction.emoji.name,
          emojiUnicode: reaction.emoji.unicode,
          emojiUrl: reaction.emoji.url,
        }),
      );

      return comment;
    });
  }

  async getPreviousCommentByCommentId(
    commentId: string,
    currentUser: CurrentUser,
  ) {
    const comment = await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: currentUser.orgId },
    });

    const previousComment = await this.commentRepository.findByCondition({
      where: {
        ticketId: comment.ticketId,
        organizationId: currentUser.orgId,
        createdAt: LessThan(comment.createdAt),
      },
      order: {
        createdAt: "DESC",
      },
    });

    if (!previousComment) {
      return null;
    }

    return previousComment;
  }
}
