import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { RedisCacheProvider } from "@repo/nestjs-commons/cache";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  BusinessHoursConfig,
  BusinessHoursConfigRepository,
  CachedBusinessHoursConfigRepository,
  CachedTeamCapacityRepository,
  CachedTeamConfigurationRepository,
  CachedTeamRepository,
  CachedTeamRoutingRulesRepository,
  Team,
  TeamCapacityRepository,
  TeamConfiguration,
  TeamConfigurationRepository,
  TeamMember,
  TeamMemberRepository,
  TeamMemberRole,
  TeamRepository,
  TeamRoutingRules,
  TeamRoutingRulesRepository,
  TeamUserRoutingStrategy,
  TransactionContext,
  TransactionService,
  User,
  UserStatus,
  UserType,
} from "@repo/thena-platform-entities";
import { DateTime } from "luxon";
import {
  DeepPartial,
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  ILike,
  In,
  IsNull,
  Not,
  QueryFailedError,
} from "typeorm";
import { IdGeneratorUtils } from "../../common";
import { CACHE_TTL } from "../../common/constants/cache.constants";
import { POSTGRES_ERROR_CODES } from "../../common/constants/postgres-errors.constants";
import { CurrentUser } from "../../common/decorators";
import { UpdateTimezoneWorkingHoursDto } from "../../common/dto";
import { BusinessHoursValidatorService } from "../../common/services/business-hours-validation.service";
import { generateIdentifier } from "../../common/utils/identifier-generator.utils";
import { constructDailyConfigFromCommonSlots } from "../../common/utils/time-slots.utils";
import { SharedService } from "../../shared/shared.service";
import { UsersService } from "../../users/services/users.service";
import { EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS } from "../constants/team-members.constants";
import {
  EAGERLY_LOADED_RELATIONS,
  TEAM_ROUTING_RULES_RELATIONS,
} from "../constants/teams.constants";
import { AddTeamMemberDto, GetTeamMembersDto } from "../dto/team-member.dto";
import {
  CreateRoutingRuleGroupDto,
  UpdateRoutingRuleGroupDto,
} from "../dto/team-routing.dto";
import { CreateTeamDto, UpdateTeamDto } from "../dto/teams.dto";
import { EmittableTeamEvents } from "../events/teams.events";
import { TeamAvailabilityResponseDto } from "../transformers";

export interface CombinedTeamConfig {
  teamId: string;
  teamConfig: TeamConfiguration;
  businessHoursConfig: BusinessHoursConfig;
}

@Injectable()
export class TeamsService {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,

    private readonly eventEmitter: EventEmitter2,

    // Teams repositories
    private readonly teamRepository: TeamRepository,
    private readonly cachedTeamRepository: CachedTeamRepository,

    // Team members repositories
    private readonly teamMemberRepository: TeamMemberRepository,

    // Team configuration repositories
    private readonly teamConfigurationRepository: TeamConfigurationRepository,
    private readonly cachedTeamConfigurationRepository: CachedTeamConfigurationRepository,

    // Team capacity repositories
    private readonly teamCapacityRepository: TeamCapacityRepository,
    private readonly cachedTeamCapacityRepository: CachedTeamCapacityRepository,

    // Team routing rules repositories
    private readonly teamRoutingRulesRepository: TeamRoutingRulesRepository,
    private readonly cachedTeamRoutingRulesRepository: CachedTeamRoutingRulesRepository,

    // Business hours configuration repositories
    private readonly businessHoursConfigRepository: BusinessHoursConfigRepository,
    private readonly cachedBusinessHoursConfigRepository: CachedBusinessHoursConfigRepository,

    // Injected services
    private readonly cacheProvider: RedisCacheProvider,
    private readonly usersService: UsersService,
    private readonly transactionService: TransactionService,
    private readonly businessHoursValidationService: BusinessHoursValidatorService,
    private readonly sharedService: SharedService,
  ) {}

  /**
   * @internal
   * Finds a team by its team ID.
   * @param teamId The team ID of the team to find.
   * @returns The team.
   */
  findOneByTeamId(
    teamId: string,
    organizationId: string,
    parentTeamId?: string,
    noRelations?: boolean,
  ) {
    this.logger.log(
      `Finding team by ID: ${teamId} for organization: ${organizationId}`,
    );
    const whereClause: FindOptionsWhere<Team> = { uid: teamId, organizationId };
    if (parentTeamId) {
      whereClause.parentTeamId = parentTeamId;
      this.logger.log(`Including parent team filter: ${parentTeamId}`);
    }

    return this.teamRepository.findByCondition({
      where: whereClause,
      relations: noRelations ? [] : EAGERLY_LOADED_RELATIONS,
    });
  }

  /**
   * @internal
   * Checks if a team exists.
   * @param whereClause The where clause to check.
   * @returns Whether the team exists.
   */
  checkExists(whereClause: FindOptionsWhere<Team>) {
    this.logger.log(
      `Checking team existence with conditions: ${JSON.stringify(whereClause)}`,
    );
    return this.teamRepository.exists({ where: whereClause });
  }

  /**
   * @internal
   * Checks if a user can read a team.
   * @param teamId The team ID.
   * @param user The user.
   * @returns The team and whether the user belongs to the team.
   */
  async canUserReadTeam(teamId: string, user: CurrentUser) {
    this.logger.log(
      `Checking read permissions for team ${teamId} by user ${user.sub}`,
    );

    // Find the team by its team ID and organization ID
    const team = await this.findOneByTeamId(teamId, user.orgId);
    if (!team) {
      this.logger.warn(`Team not found: ${teamId}`);
      throw new NotFoundException("Team not found!");
    }

    // Check if the user belongs to the team
    const userBelongsToTeam = await this.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // If the team is private, perform additional checks
    if (team.isPrivate) {
      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        this.logger.warn(
          `User ${user.sub} attempted to access private team ${teamId} without membership`,
        );
        throw new NotFoundException("Team not found!");
      }
    }

    this.logger.log(
      `User ${user.sub} ${
        userBelongsToTeam ? "belongs to" : "can read"
      } team ${teamId}`,
    );
    return { team, userInTeam: userBelongsToTeam };
  }

  /**
   * @internal
   * Checks if a user can update a team.
   * @param teamId The team ID.
   * @param user The user.
   * @returns The team and whether the user belongs to the team.
   */
  async canUserUpdateTeam(
    teamId: string,
    user: CurrentUser,
    checkOwner: boolean = false,
  ) {
    this.logger.log(
      `Checking update permissions for team ${teamId} by user ${user.sub}`,
    );

    const { team, userInTeam } = await this.canUserReadTeam(teamId, user);

    // If the user does not belong to the team, throw an error
    if (!userInTeam) {
      this.logger.warn(
        `User ${user.sub} attempted to update team ${teamId} without membership`,
      );
      throw new ForbiddenException(
        "You need to be a member of this team to update the resource!",
      );
    }

    // If the user is checking if they are the team owner, perform the check
    if (checkOwner) {
      // If the user is not the team owner, throw an error
      const isOwner = user.sub === team.teamOwnerId;
      const isAdmin = userInTeam.role === TeamMemberRole.ADMIN;
      if (!isOwner && !isAdmin) {
        this.logger.warn(
          `User ${user.sub} attempted owner/admin action on team ${teamId} without proper permissions`,
        );
        throw new ForbiddenException(
          "Only the team owner or admins can perform this action!",
        );
      }
      this.logger.log(
        `User ${user.sub} authorized as ${
          isOwner ? "owner" : "admin"
        } for team ${teamId}`,
      );
    }

    return { team, userInTeam };
  }

  async getTeamsByUser(
    user: CurrentUser,
    select?: FindOptionsSelect<TeamMember>,
    relations?: FindOptionsRelations<TeamMember>,
  ) {
    try {
      this.logger.log(`Fetching teams for user SUB: ${user.sub}`);
      this.logger.log(`Fetching teams for user ORD: ${user.orgId}`);

      this.logger.log(`user ${JSON.stringify(user)}`);

      const teams = await this.teamMemberRepository.findAll({
        where: { userId: user.sub, organizationId: user.orgId },
        select,
        relations,
      });
      this.logger.log(`Teams: ${JSON.stringify(teams)}`);
      return teams;
    } catch (error) {
      this.logger.error(`Error fetching teams for user ${user.uid}`, error);
      throw error;
    }
  }

  /**
   * @internal
   * Gets a user and team by their IDs and organization ID. This also validates if the user belongs to the team.
   * @param teamId The team ID.
   * @param userId The user ID.
   * @param organizationId The organization ID.
   * @returns The user and team.
   */
  async getUserAndTeam(teamId: string, userId: string, organizationId: string) {
    // Look up the team if it exists
    const team = await this.findOneByTeamId(teamId, organizationId);
    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    // Look up the team member if it exists
    const teamMember = await this.teamMemberRepository.findByCondition({
      where: { teamId: team.id, userId, organizationId },
    });

    // If the user is not found in the team, throw an error
    if (!teamMember) {
      throw new ForbiddenException("User is not a member of this team!");
    }

    return { team };
  }

  /**
   * @internal
   * Creates a new team member.
   * @param teamMember The team member data to create.
   * @returns The created team member.
   */
  async createTeamMember(
    teamMember: Partial<TeamMember>,
    context?: TransactionContext,
    upsert?: boolean,
  ): Promise<TeamMember> {
    const newTeamMember = this.teamMemberRepository.create(teamMember);
    if (context) {
      if (upsert) {
        await this.teamMemberRepository.upsertWithTxn(context, newTeamMember, {
          conflictPaths: ["teamId", "userId", "organizationId"],
        });
      } else {
        return this.teamMemberRepository.saveWithTxn(context, newTeamMember);
      }

      return this.teamMemberRepository.findByCondition({
        where: {
          teamId: newTeamMember.teamId,
          userId: newTeamMember.userId,
          organizationId: newTeamMember.organizationId,
        },
      });
    }

    if (upsert) {
      await this.teamMemberRepository.upsert(newTeamMember, {
        conflictPaths: ["teamId", "userId", "organizationId"],
      });
    } else {
      return this.teamMemberRepository.save(newTeamMember);
    }

    return this.teamMemberRepository.findByCondition({
      where: {
        teamId: newTeamMember.teamId,
        userId: newTeamMember.userId,
        organizationId: newTeamMember.organizationId,
      },
    });
  }

  /**
   * @internal
   * Finds teams by their public IDs.
   * @param teamIds The team IDs to find.
   * @param organizationId The organization ID.
   * @returns The teams.
   */
  findTeamsByPublicIds(teamIds: string[], organizationId: string) {
    return this.teamRepository.findAll({
      where: { uid: In(teamIds), organizationId },
    });
  }

  /**
   * @private
   * Finds a team by its team ID from the request.
   * @param teamId The team ID of the team to find.
   * @param request The request object.
   * @returns The team.
   */
  private async findTeamFromUser(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const team = await this.findOneByTeamId(teamId, user.orgId);
    return team;
  }

  /**
   * @internal
   * Checks if a user belongs to a team.
   * @param userId The user ID.
   * @param teamId The team ID.
   * @param organizationId The organization ID.
   * @returns Whether the user belongs to the team.
   */
  async userBelongsToTeam(
    userId: string,
    team: Team | string,
    organizationId: string,
    options: {
      relations?: FindOptionsRelations<TeamMember>;
    } = {},
  ) {
    let teamLookup: Team | null = null;
    if (typeof team === "string") {
      teamLookup = await this.teamRepository.findByCondition({
        where: { id: team, organizationId },
      });
    } else if (typeof team === "object" && team?.uid?.startsWith("T")) {
      teamLookup = team;
    } else {
      throw new BadRequestException("Invalid team provided!");
    }

    const whereConditions = [
      // User is a member of the team
      { organizationId, userId, teamId: teamLookup.id },
    ];

    // Only add parent team check if it's a sub-team
    if (teamLookup.parentTeamId) {
      whereConditions.push({
        organizationId,
        userId,
        teamId: teamLookup.parentTeamId,
      });
    }
    // Find the team member
    const teamMember = await this.teamMemberRepository.findByCondition({
      where: whereConditions,
      relations: options.relations,
    });

    return teamMember;
  }

  /**
   * @internal
   * Removes a team member from a team.
   * @param teamMemberId The team member ID to remove.
   * @returns The removed team member.
   */
  async removeTeamMember(teamMemberId: string, teamId: string) {
    // Find the team member to remove
    const teamMemberToRemove = await this.teamMemberRepository.findByCondition({
      where: { userId: teamMemberId, teamId },
    });

    // If the team member is not found, throw an error
    if (!teamMemberToRemove) {
      throw new NotFoundException(
        "Team member not found! Or not a member of this team.",
      );
    }

    // Remove the team member
    const removedTeamMember = await this.teamMemberRepository.remove(
      teamMemberToRemove,
    );

    return removedTeamMember;
  }

  /**
   * Finds a team by its team ID.
   * @param teamId The team ID of the team to find.
   * @returns The team.
   */
  async findOneTeamById(teamId: string, user: CurrentUser) {
    const { team } = await this.canUserReadTeam(teamId, user);
    const teamConfig =
      await this.cachedTeamConfigurationRepository.findByCondition({
        where: { teamId: team.id },
        relations: { fallbackSubTeam: true },
      });

    return { ...team, configuration: teamConfig };
  }

  /**
   * Finds the members of a team.
   * @param teamId The team ID of the team to find the members of.
   * @param request The request object.
   * @returns The team members.
   */
  async findTeamMembers(
    teamId: string,
    user: CurrentUser,
    query?: GetTeamMembersDto,
  ) {
    const { team } = await this.canUserReadTeam(teamId, user);
    const searchQuery = query?.searchQuery?.toLowerCase();

    const whereClause: FindOptionsWhere<TeamMember> = {
      teamId: team.id,
      user: { userType: Not(UserType.BOT_USER) },
    };
    if (searchQuery) {
      whereClause.user = {
        userType: Not(UserType.BOT_USER),
        name: ILike(`%${searchQuery}%`),
      };
    }

    // Find all team members for the team
    const teamMembers = await this.teamMemberRepository.findAll({
      where: whereClause,
      relations: EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS,
    });

    return teamMembers;
  }

  /**
   * Finds all active team members for a team.
   * @param teamId The team ID of the team to find the members of.
   * @param user The user making the request.
   * @returns The team members.
   */
  async findActiveTeamMembers(teamId: string, user: CurrentUser) {
    const team = await this.findTeamFromUser(teamId, user);
    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    // Find all team members for the team
    const members = await this.teamMemberRepository.findAll({
      where: {
        teamId: team.id,
        organizationId: user.orgId,
        user: { status: UserStatus.ACTIVE, userType: Not(UserType.BOT_USER) },
      },
      relations: {
        user: { businessHoursConfig: true, userTeamCapacity: true },
      },
    });

    return members;
  }

  /**
   * Finds the team capacity for a team.
   * @param teamId The team ID of the team to find the capacity of.
   * @param user The user making the request.
   * @returns The team capacity.
   */
  async getTeamCapacity(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const team = await this.findOneByTeamId(teamId, user.orgId);
    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    // Find the team capacity by its team ID and organization ID
    const teamCapacity =
      await this.cachedTeamCapacityRepository.findByCondition({
        where: { team: { id: team.id }, organization: { id: user.orgId } },
      });

    return teamCapacity;
  }

  /**
   * Finds all the sub teams that belong to a parent team
   * @param user Currently logged in user
   * @param parentTeamId UID of the parent team
   * @return The teams
   */
  async findAllSubTeams(user: CurrentUser, parentTeamId: string) {
    this.logger.log(`Fetching sub-teams for parent team ${parentTeamId}`);
    // Find the parent team first
    const parentTeam = await this.cachedTeamRepository.findByCondition({
      where: { uid: parentTeamId, organizationId: user.orgId },
    });

    // If the parent team is not found, throw an error
    if (!parentTeam) {
      this.logger.warn(`Parent team ${parentTeamId} not found`);
      throw new NotFoundException("Parent team not found!");
    }

    // Find the sub-teams
    const subTeams = await this.teamRepository.findAll({
      where: {
        parentTeamId: parentTeam.id,
        deletedAt: IsNull(),
        organizationId: user.orgId,
      },
      order: { createdAt: "DESC" },
    });

    this.logger.log(
      `Found ${subTeams.length} sub-teams for parent team ${parentTeamId}`,
    );
    return subTeams;
  }

  /**
   * Finds all the teams by user
   * @param user User
   * @param parentTeamId ID of the parent team
   * @return The teams
   */
  async findAllTeamsByUser(user: User, parentTeamId?: string) {
    this.logger.log(
      `Fetching teams for user ${user.uid}${
        parentTeamId ? ` with parent team ${parentTeamId}` : ""
      }`,
    );

    const whereClause: FindOptionsWhere<TeamMember> = {
      organizationId: user.organizationId,
      userId: user.id,
      isActive: true,
      team: {
        deletedAt: IsNull(),
        ...(parentTeamId && { parentTeamId }),
      },
    };

    const userInTeams = await this.teamMemberRepository.findAll({
      where: whereClause,
      relations: {
        team: {
          parentTeam: true,
        },
      },
      order: { team: { createdAt: "DESC" } },
    });

    const teams = userInTeams
      .map((userInTeam) => userInTeam.team)
      .filter(Boolean);

    this.logger.log(`Found ${teams.length} teams for user ${user.uid}`);
    return teams;
  }

  /**
   * Finds all teams for the user
   * @returns The teams.
   */
  async findAllTeams(user: CurrentUser, parentTeamId?: string) {
    this.logger.log(`Fetching all teams for user ${user.uid}`);
    // Construct the initial where clause
    let whereClause: FindOptionsWhere<TeamMember> = {
      organizationId: user.orgId,
      userId: user.sub,
    };

    // If parent team id is provided add it as a filter
    if (parentTeamId) {
      this.logger.log(`Filtering by parent team: ${parentTeamId}`);
      whereClause = {
        ...whereClause,
        team: { parentTeamId, deletedAt: IsNull() },
      };
    }

    // Find all teams for the organization
    const userInTeams = await this.teamMemberRepository.findAll({
      where: whereClause,
      relations: {
        team: {
          parentTeam: true,
          teamOwner: true,
        },
      },
      order: { team: { createdAt: "DESC" } },
    });

    // Format the teams
    const teams = userInTeams
      .map((userInTeam) => userInTeam.team)
      .filter(Boolean);

    this.logger.log(`Found ${teams.length} teams for user ${user.uid}`);
    return teams;
  }

  /**
   * Finds all public teams for the organization.
   * @returns The teams.
   */
  async getAllPublicTeams(user: CurrentUser) {
    this.logger.log(`Fetching all public teams for organization ${user.orgId}`);
    const teams = await this.teamRepository.findAll({
      where: { organizationId: user.orgId, isPrivate: false },
      relations: { parentTeam: true },
    });

    this.logger.log(`Found ${teams.length} public teams`);
    return teams;
  }

  /**
   * Creates a new team.
   * @param createTeamDto The team data to create.
   * @returns The created team.
   */
  async createTeam(createTeamDto: CreateTeamDto, user: CurrentUser) {
    this.logger.log(
      `Creating new team "${createTeamDto.name}" for organization ${user.orgId}`,
    );
    try {
      const createdTeam = await this.transactionService.runInTransaction(
        async (txnContext) => {
          // Generate a unique identifier for the team
          const teamId = this.generateTeamIdentifier();
          this.logger.log(`Generated team identifier: ${teamId}`);

          // Get the user email from the user object attached to the request
          const userEmail = user.email;
          if (!userEmail) {
            this.logger.error(`User ${user.sub} has no email address`);
            throw new UnauthorizedException("User is not authenticated!");
          }

          // If the identifier is not provided, generate a unique identifier for the team
          if (!createTeamDto.identifier) {
            createTeamDto.identifier = generateIdentifier(createTeamDto.name);
            this.logger.log(
              `Generated team identifier from name: ${createTeamDto.identifier}`,
            );
          }

          // If the parent team ID is provided, find the parent team
          let parentTeam: Team | null;
          if (createTeamDto.parentTeamId) {
            this.logger.log(
              `Finding parent team: ${createTeamDto.parentTeamId}`,
            );
            parentTeam = await this.findOneByTeamId(
              createTeamDto.parentTeamId,
              user.orgId,
            );

            // If the parent team is a sub-team, throw an error
            if (parentTeam?.parentTeamId) {
              this.logger.warn(
                `Attempted to create sub-team inside sub-team ${parentTeam.uid}`,
              );
              throw new BadRequestException(
                "Cannot create a sub-team inside a sub-team!",
              );
            }

            // If the parent team is not found, throw an error
            if (!parentTeam) {
              this.logger.warn(
                `Parent team ${createTeamDto.parentTeamId} not found`,
              );
              throw new NotFoundException("Parent team not found!");
            }
          }

          // Create the team
          this.logger.log("Creating new team record");
          const newTeam = await this.teamRepository.saveWithTxn(txnContext, {
            uid: teamId,
            icon: createTeamDto.icon,
            color: createTeamDto.color,
            identifier: createTeamDto.identifier,
            name: createTeamDto.name,
            description: createTeamDto.description,
            teamOwnerId: user.sub,
            organizationId: user.orgId,
            parentTeamId: parentTeam?.id,
            isPrivate: createTeamDto?.isPrivate,
          });

          this.logger.log(
            `Creating business hours config for team ${newTeam.uid}`,
          );
          // Create team's business hours config
          const businessHoursConfig =
            await this.businessHoursConfigRepository.saveWithTxn(txnContext, {
              teamId: newTeam.id,
              organizationId: user.orgId,
              monday: {
                isActive: true,
                slots: [{ start: "00:00", end: "23:59" }],
              },
              tuesday: {
                isActive: true,
                slots: [{ start: "00:00", end: "23:59" }],
              },
              wednesday: {
                isActive: true,
                slots: [{ start: "00:00", end: "23:59" }],
              },
              thursday: {
                isActive: true,
                slots: [{ start: "00:00", end: "23:59" }],
              },
              friday: {
                isActive: true,
                slots: [{ start: "00:00", end: "23:59" }],
              },
              saturday: {
                isActive: true,
                slots: [{ start: "00:00", end: "23:59" }],
              },
              sunday: {
                isActive: true,
                slots: [{ start: "00:00", end: "23:59" }],
              },
            });

          this.logger.log(
            `Creating team configurations for team ${newTeam.uid}`,
          );
          // Create team configurations
          const teamConfigurations =
            await this.teamConfigurationRepository.saveWithTxn(txnContext, {
              teamId: newTeam.id,
              organizationId: user.orgId,
              userRoutingStrategy: TeamUserRoutingStrategy.MANUAL,
              businessHoursConfigId: businessHoursConfig.id,
            });

          // Update the team with its configs
          await this.teamRepository.updateWithTxn(
            txnContext,
            { id: newTeam.id },
            { configurationId: teamConfigurations.id },
          );

          // Seed default status, priority and type if the team is not a sub-team
          if (!createTeamDto.parentTeamId) {
            this.logger.log(
              `Seeding default status, priority and type for team ${newTeam.uid}`,
            );
            await this.sharedService.seedDefaultStatusPriorityAndType(
              newTeam,
              txnContext,
            );
          }

          if (!createTeamDto.parentTeamId) {
            this.logger.log(
              `Creating team member record for owner ${user.sub}`,
            );
            // Create the team member and make him the member
            await this.createTeamMember(
              {
                teamId: newTeam.id,
                userId: user.sub,
                isActive: true,
                organizationId: user.orgId,
                role: TeamMemberRole.ADMIN,
              },
              txnContext,
            );
          }

          return newTeam;
        },
      );

      const returnableTeam = await this.findOneTeamById(createdTeam.uid, user);
      this.logger.log(`Successfully created team ${returnableTeam.uid}`);
      this.eventEmitter.emit(EmittableTeamEvents.TEAM_CREATED, {
        teamId: returnableTeam.id,
        teamUID: returnableTeam.uid,
        organizationId: user.orgId,
        organizationUID: user.orgUid,
      });
      return returnableTeam;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        if (error.code === POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
          // If the error is due to a duplicate team name, throw an error
          if ((error as any)?.constraint === "unique_team_name_ex_deleted") {
            this.logger.warn(
              `Team name "${createTeamDto.name}" already exists`,
            );

            throw new ConflictException(
              "Name already in use by a team or group. Try a different name.",
            );
          }

          // If the error is due to a duplicate team ID, throw an error
          if ((error as any)?.constraint === "unique_team_id") {
            this.logger.error("Generated duplicate team ID");
            throw new InternalServerErrorException(
              "Something went wrong while creating the team! Please try again.",
            );
          }
        }
      }

      // Log the error and propagate it
      this.logger.error("Failed to create team:", error);
      throw error;
    }
  }

  /**
   * Updates a team.
   * @param teamId The team ID to update.
   * @param updateTeamDto The team data to update.
   * @param user The user making the request.
   * @returns The updated team.
   */
  async updateTeam(
    teamId: string,
    updateTeamDto: UpdateTeamDto,
    user: CurrentUser,
  ) {
    this.logger.log(`Updating team ${teamId} by user ${user.uid}`);

    // Find the team by its team ID and organization ID
    const { team } = await this.canUserUpdateTeam(teamId, user, true);

    // If the identifier is provided, check if it is unique
    if (updateTeamDto.identifier) {
      this.logger.log(
        `Checking if identifier "${updateTeamDto.identifier}" is unique`,
      );
      const identifierExists = await this.teamRepository.exists({
        where: {
          identifier: updateTeamDto.identifier,
          organizationId: user.orgId,
          id: Not(team.id),
        },
      });

      // If the identifier already exists, throw an error
      if (identifierExists) {
        this.logger.warn(
          `Team identifier "${updateTeamDto.identifier}" already exists`,
        );
        throw new ConflictException(
          "Identifier already exists for another team!",
        );
      }
    }

    try {
      // Update the provided team with the new data
      await this.transactionService.runInTransaction(async (txnContext) => {
        this.logger.log(`Updating team ${teamId} properties`);
        // Update the team
        const updateTeamResult = await this.teamRepository.updateWithTxn(
          txnContext,
          { id: team.id },
          {
            name: updateTeamDto.name,
            description: updateTeamDto.description,
            isPrivate: updateTeamDto.isPrivate,
            icon: updateTeamDto.icon,
            color: updateTeamDto.color,
            identifier: updateTeamDto.identifier,
          },
        );

        this.logger.log(`Invalidating cache for team ${teamId}`);
        // Invalidate the query
        await this.cachedTeamRepository.invalidateQuery({
          where: { uid: teamId, organizationId: user.orgId },
          relations: EAGERLY_LOADED_RELATIONS,
        });

        return updateTeamResult;
      });

      // Fetch the returnable team
      const returnableTeam = await this.findOneTeamById(teamId, user);
      this.logger.log(`Successfully updated team ${teamId}`);
      return returnableTeam;
    } catch (error) {
      this.logger.error(`Failed to update team ${teamId}:`, error);
      throw error;
    }
  }

  /**
   * Deletes a team.
   * @param teamId The team ID to delete.
   * @param request The request object.
   * @returns The deleted team.
   */
  async deleteOneTeam(teamId: string, user: CurrentUser) {
    this.logger.log(`Attempting to delete team ${teamId} by user ${user.uid}`);

    // Find the team by its team ID and organization ID
    const { team } = await this.canUserUpdateTeam(teamId, user, true);

    // Delete team and team configurations in a transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      const commonOptions = {
        id: team.configurationId,
        teamId: team.id,
        organizationId: user.orgId,
      };

      this.logger.log(`Deleting business hours config for team ${teamId}`);
      // Delete the business hours config
      await this.cachedBusinessHoursConfigRepository.softDeleteWithTxn(
        txnContext,
        commonOptions,
      );

      this.logger.log(`Deleting team configurations for team ${teamId}`);
      // Delete the team configurations
      await this.cachedTeamConfigurationRepository.softDeleteWithTxn(
        txnContext,
        commonOptions,
      );

      this.logger.log(`Deleting team members for team ${teamId}`);
      // Delete the team members
      await this.teamMemberRepository.softRemoveByConditionTxn(txnContext, {
        where: { teamId: team.id, organizationId: user.orgId },
      });

      this.logger.log(`Deleting team ${teamId}`);
      // Delete the team
      await this.cachedTeamRepository.softDeleteWithTxn(txnContext, {
        id: team.id,
        uid: team.uid,
        organizationId: user.orgId,
      });

      this.logger.log(`Invalidating cache for team ${teamId}`);
      await this.cachedTeamRepository.invalidateTeamCacheWithKey({
        where: { uid: team.uid, organizationId: user.orgId },
        relations: EAGERLY_LOADED_RELATIONS,
      });
    });

    this.logger.log(`Successfully deleted team ${teamId}`);
  }

  /**
   * Adds a team member to a team.
   * @param teamId The team ID to add the member to.
   * @param addTeamMemberDto The team member data to add.
   * @param request The request object.
   * @returns The added team member.
   */
  async addMemberToTeam(
    teamId: string,
    addTeamMemberDto: AddTeamMemberDto,
    user: CurrentUser,
  ) {
    this.logger.log(`Adding member to team ${teamId} by user ${user.uid}`);
    let userToAdd: User | null;

    // Find the user by their email address
    if (addTeamMemberDto.email) {
      this.logger.log(`Finding user by email: ${addTeamMemberDto.email}`);
      userToAdd = await this.usersService.findOneByEmail(
        addTeamMemberDto.email,
        user.orgId,
      );
    } else if (addTeamMemberDto.userId) {
      this.logger.log(`Finding user by ID: ${addTeamMemberDto.userId}`);
      userToAdd = await this.usersService.findOneByPublicId(
        addTeamMemberDto.userId,
        user.orgId,
      );
    }

    // If the user to add is not found, throw an error
    if (!userToAdd) {
      this.logger.warn("User to add not found");
      throw new NotFoundException("User not found!");
    }

    // Find the team by its team ID and organization ID
    const team = await this.findOneByTeamId(teamId, user.orgId);
    if (!team) {
      this.logger.warn(`Team ${teamId} not found`);
      throw new NotFoundException("Team not found!");
    }

    // Check if the user who is inviting and the user to add belong to the same organization
    const teamOrg = team.organizationId;
    if (teamOrg !== user.orgId && teamOrg !== userToAdd.organizationId) {
      this.logger.warn(
        `Invalid organization invite attempt: inviter org ${user.orgId}, invitee org ${userToAdd.organizationId}`,
      );
      throw new ForbiddenException("Invalid foreign organization invite!");
    }

    // Check if the user who is inviting belongs to the team
    const userBelongsToTeam = await this.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // Checks to perform if the team is private
    if (team.isPrivate) {
      // User is self-adding to a private team, throw a Not Found Exception
      // to prevent user from checking if the team exists
      if (userToAdd.id === user.sub) {
        this.logger.warn(
          `User ${user.sub} attempted to self-add to private team ${teamId}`,
        );
        throw new NotFoundException("Team not found!");
      }

      // If the user is not the team owner, throw an error
      if (user.sub !== team.teamOwnerId) {
        this.logger.warn(
          `Non-owner ${user.sub} attempted to invite to private team ${teamId}`,
        );
        throw new ForbiddenException(
          "Only the team owner can invite new members to a private team!",
        );
      }

      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        this.logger.warn(
          `User ${user.sub} attempted to invite to team ${teamId} without membership`,
        );
        throw new NotFoundException("Team not found!");
      }
    }

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam && user.sub !== userToAdd.id) {
      this.logger.warn(
        `User ${user.sub} attempted to invite without team membership`,
      );
      throw new ForbiddenException(
        "You need to be a member of this team to invite new members!",
      );
    }

    // CHECK: If the user is not an admin and is trying to add an admin, throw an error
    // NOTE: Do not check if the user's role is MEMBER because you might end up adding
    // more roles but the admin remains admin therefore the NOT check is better
    const isMember =
      userBelongsToTeam && userBelongsToTeam.role !== TeamMemberRole.ADMIN;
    if (addTeamMemberDto.isAdmin && isMember) {
      throw new ForbiddenException(
        "Only admins can add new admins to the team!",
      );
    }

    try {
      this.logger.log(
        `Creating team member record for user ${userToAdd.id} in team ${teamId}`,
      );
      // Create the team member
      const teamMember = await this.createTeamMember(
        {
          teamId: team.id,
          userId: userToAdd.id,
          invitedById: user.sub,
          organizationId: user.orgId,
        },
        null,
        true,
      );

      // If the team has a parent team, add the user to the parent team
      if (team.parentTeamId) {
        this.logger.log(
          `Adding user ${userToAdd.id} to parent team ${team.parentTeamId}`,
        );
        await this.createTeamMember(
          {
            teamId: team.parentTeamId,
            userId: userToAdd.id,
            invitedById: user.sub,
            organizationId: user.orgId,
          },
          null,
          true,
        );
      }

      // Find the team member by its ID
      const returnableTeamMember =
        await this.teamMemberRepository.findByCondition({
          where: {
            id: teamMember.id,
            teamId: team.id,
            userId: userToAdd.id,
            organizationId: user.orgId,
          },
          relations: EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS,
        });

      this.logger.log(
        `Successfully added user ${userToAdd.id} to team ${teamId}`,
      );
      return returnableTeamMember;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        if (error.code === POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
          // If the error is due to a duplicate team name, throw an error
          if ((error as any)?.constraint === "unique_team_member") {
            this.logger.warn(
              `User ${userToAdd.id} is already a member of team ${teamId}`,
            );
            throw new ConflictException(
              "This user is already a member of this team!",
            );
          }
        }
      }

      this.logger.error(`Failed to add member to team ${teamId}:`, error);
      throw error;
    }
  }

  /**
   * Removes a team member from a team.
   * @param teamId The team ID to remove the member from.
   * @param memberId The member ID to remove from the team.
   * @param request The request object.
   * @returns The removed team member.
   */
  async removeMemberFromTeam(
    teamId: string,
    memberId: string,
    user: CurrentUser,
  ) {
    this.logger.log(
      `Removing member ${memberId} from team ${teamId} by user ${user.uid}`,
    );

    // Find the user to remove and the team
    const [userToRemove, team] = await Promise.all([
      this.usersService.findOneByPublicId(memberId),
      this.findOneByTeamId(teamId, user.orgId),
    ]);

    // If the user to remove is not found, throw an error
    if (!userToRemove) {
      this.logger.warn(`User ${memberId} not found for removal`);
      throw new NotFoundException("User not found!");
    }

    // If the team is not found, throw an error
    if (!team) {
      this.logger.warn(`Team ${teamId} not found for member removal`);
      throw new NotFoundException("Team not found!");
    }

    // Check if the user belongs to the team
    const userBelongsToTeam = await this.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // Checks to perform if the team is private
    if (team.isPrivate) {
      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        this.logger.warn(
          `User ${user.sub} attempted to remove member from private team ${teamId} without membership`,
        );
        throw new NotFoundException("Team not found!");
      }
    }

    // If the team is not a sub-team, check if the user is the team owner
    if (!team.parentTeamId) {
      // If the user is not the team owner, throw an error
      if (user.sub !== team.teamOwnerId && user.uid !== memberId) {
        this.logger.warn(
          `Non-owner ${user.sub} attempted to remove member from team ${teamId}`,
        );
        throw new ForbiddenException(
          "Only the team owner can remove members from the team!",
        );
      }

      // If the user to remove is the team owner, throw an error
      if (team.teamOwner.uid === memberId) {
        // If the user trying to delete the team owner is not the team owner
        if (user.sub !== team.teamOwnerId) {
          this.logger.warn(
            `User ${user.sub} attempted to remove team owner from team ${teamId}`,
          );
          throw new ForbiddenException("Cannot remove the team owner!");
        }

        // If the user is the team owner, throw an error
        this.logger.warn(
          `Team owner ${memberId} attempted to remove themselves from team ${teamId}`,
        );
        throw new ForbiddenException(
          "You are the owner of this team, please archive or delete the team instead.",
        );
      }
    }

    // Remove the team member
    await this.removeTeamMember(userToRemove.id, team.id);
    this.logger.log(
      `Successfully removed member ${memberId} from team ${teamId}`,
    );
  }

  /**
   * Gets a team's configurations.
   * @param teamId The team ID to get the configurations of.
   * @param user The user making the request.
   * @returns The team configurations.
   */
  async getTeamConfigurations(
    teamId: string,
    user: CurrentUser,
  ): Promise<CombinedTeamConfig> {
    this.logger.log(`Fetching configurations for team ${teamId}`);
    const { team } = await this.canUserUpdateTeam(teamId, user);

    const teamConfigLookupClause = {
      where: { teamId: team.id },
      relations: { fallbackSubTeam: true },
    };
    const businessHoursConfigLookupClause = { where: { teamId: team.id } };
    this.logger.log(
      `Retrieving team config and business hours for team ${teamId}`,
    );
    const [teamConfig, businessHoursConfig] = await Promise.all([
      this.teamConfigurationRepository.findByCondition(teamConfigLookupClause),
      this.businessHoursConfigRepository.findByCondition(
        businessHoursConfigLookupClause,
      ),
    ]);

    return {
      teamId: team.uid,
      teamConfig,
      businessHoursConfig,
    };
  }

  /**
   * Updates a team's configurations and business hours.
   * @param teamId The team ID to update the configurations of.
   * @param updateTeamsDto The team configurations data to update.
   * @param user The user making the request.
   * @returns The updated team configurations.
   */
  async updateTeamConfigurations(
    teamId: string,
    updateTeamsDto: UpdateTimezoneWorkingHoursDto,
    user: CurrentUser,
  ) {
    this.logger.log(
      `Updating configurations for team ${teamId} by user ${user.uid}`,
    );
    const {
      holidays,
      timezone,
      dailyConfig,
      commonDailyConfig,
      commonSlots,
      routingRespectsTimezone,
      routingRespectsUserTimezone,
      routingRespectsUserAvailability,
      routingRespectsUserBusinessHours,
      userRoutingStrategy,
      routingRespectsUserCapacity,
      fallbackSubTeam,
    } = updateTeamsDto;

    // Check if the user can update the team
    const { team } = await this.canUserUpdateTeam(teamId, user);

    const currentTeamConfig =
      await this.cachedTeamConfigurationRepository.findByCondition({
        where: { teamId: team.id },
        relations: { fallbackSubTeam: true },
      });

    // If the common daily config is enabled, validate configurations
    if (commonDailyConfig) {
      // If the common slots are not provided, throw an error
      if (!commonSlots) {
        this.logger.warn(
          "Common slots required but not provided for common daily config",
        );
        throw new BadRequestException(
          "Common slots are required when common daily config is enabled!",
        );
      }

      // If the daily config is provided, throw an error
      if (dailyConfig) {
        this.logger.warn("Both common daily config and daily config provided");
        throw new BadRequestException(
          "Cannot update both common daily config and daily config!",
        );
      }
    }

    // If the business hours are being updated, validate them
    if (dailyConfig) {
      this.logger.log("Validating business hours configuration");
      const tz = timezone || currentTeamConfig.timezone || "UTC";
      const validationResult =
        this.businessHoursValidationService.validateBusinessHours(
          dailyConfig,
          tz,
        );

      // If the business hours are invalid, throw an error
      if (!validationResult.isValid) {
        this.logger.warn(
          `Invalid business hours configuration: ${validationResult.error}`,
        );
        throw new BadRequestException(validationResult.error);
      }
    }

    // If the fallback sub team is provided, validate it
    let fallBackTeam: Team;
    if (fallbackSubTeam) {
      this.logger.log(`Validating fallback sub-team ${fallbackSubTeam}`);
      // If the fallback sub team is the same as the team, throw an error
      if (fallbackSubTeam === team.uid) {
        this.logger.warn("Fallback sub-team cannot be the same as the team");
        throw new BadRequestException(
          "Fallback sub team cannot be the same as the team!",
        );
      }

      // Check if the fallback sub team exists
      fallBackTeam = await this.teamRepository.findByCondition({
        where: {
          uid: fallbackSubTeam,
          parentTeamId: team.id,
          organizationId: user.orgId,
        },
      });

      // If the fallback sub team is not found, throw an error
      if (!fallBackTeam) {
        this.logger.warn(`Fallback sub-team ${fallbackSubTeam} not found`);
        throw new NotFoundException("Fallback sub team not found!");
      }
    }

    try {
      // Update the team configurations in a transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        const commonOptions = {
          id: team.configurationId,
          teamId: team.id,
          organizationId: user.orgId,
        };

        const commonCacheOptions = {
          teamId: team.id,
        };

        // Create the update set
        const updateSet: DeepPartial<TeamConfiguration> = {
          holidays,
          timezone,
          routingRespectsTimezone,
          routingRespectsUserTimezone,
          routingRespectsUserAvailability,
          routingRespectsUserBusinessHours,
          routingRespectsUserCapacity,
          userRoutingStrategy,
        };

        // If the fallback sub team is provided, update the fallback sub team
        if (fallBackTeam) {
          updateSet.fallbackSubTeam = { id: fallBackTeam.id };
        }

        this.logger.log(`Updating team configuration for team ${teamId}`);
        // Update the team's timezone
        await this.teamConfigurationRepository.updateWithTxn(
          txnContext,
          commonOptions,
          updateSet,
        );

        // Update the business hours config
        if (dailyConfig) {
          this.logger.log(`Updating business hours config for team ${teamId}`);
          await this.businessHoursConfigRepository.updateWithTxn(
            txnContext,
            { organizationId: user.orgId, teamId: team.id },
            { ...dailyConfig, commonDailyConfig: false },
          );

          // Invalidate the business hours cache
          await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
            commonCacheOptions,
          );
        }

        // If the common daily config is enabled, update the configurations
        if (commonDailyConfig && commonSlots) {
          this.logger.log(`Updating common daily config for team ${teamId}`);
          const dailyConfig = constructDailyConfigFromCommonSlots(commonSlots);
          await this.businessHoursConfigRepository.updateWithTxn(
            txnContext,
            { organizationId: user.orgId, teamId: team.id },
            { ...dailyConfig, commonDailyConfig },
          );

          // Invalidate the business hours cache
          await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
            commonCacheOptions,
          );
        }

        this.logger.log(
          `Invalidating team configuration cache for team ${teamId}`,
        );
        // Invalidate the team configuration cache
        await this.cachedTeamConfigurationRepository.invalidateTeamConfigurationCache(
          commonCacheOptions,
        );
      });

      const lookupClause = { where: { teamId: team.id } };
      const [teamConfig, businessHoursConfig] = await Promise.all([
        this.teamConfigurationRepository.findByCondition({
          where: lookupClause.where,
          relations: { fallbackSubTeam: true },
        }),
        this.businessHoursConfigRepository.findByCondition(lookupClause),
      ]);

      // If the business hours or team configurations are not found, throw an error
      if (!businessHoursConfig || !teamConfig) {
        this.logger.error(
          `Team configurations in inconsistent state for team ${teamId}`,
        );
        throw new NotFoundException(
          "Team configurations are in an inconsistent state!",
        );
      }

      this.logger.log(`Successfully updated configurations for team ${teamId}`);
      return {
        teamId: team.uid,
        teamConfig,
        businessHoursConfig,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update team configurations for team ${teamId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Creates a routing rule for a team.
   * @param teamId The team ID to create the routing rule for.
   * @param user The user making the request.
   * @param createRoutingRuleDto The routing rule data to create.
   * @returns The created routing rule.
   */
  async createRoutingRule(
    teamId: string,
    user: CurrentUser,
    createRoutingRuleDto: CreateRoutingRuleGroupDto,
  ) {
    this.logger.log(
      `Creating routing rule for team ${teamId} by user ${user.uid}`,
    );
    // Check if the user have permission to create team routing rules
    const { team } = await this.canUserUpdateTeam(teamId, user);

    // Does sub-teams exist
    const doesSubTeamsExist = await this.teamRepository.exists({
      where: {
        parentTeamId: team.id,
        organizationId: user.orgId,
      },
    });

    // If sub-teams do not exist, throw an error since we cannot create routing
    // rules for a team that does not have sub-teams
    if (!doesSubTeamsExist) {
      this.logger.warn(
        `Cannot create routing rules for team ${teamId} without sub-teams`,
      );
      throw new BadRequestException(
        "Cannot create routing rules for a team that does not have sub-teams!",
      );
    }

    // Check if the result team exists
    const doesResultTeamExists = await this.teamRepository.exists({
      where: {
        uid: createRoutingRuleDto.resultTeamId,
        organizationId: user.orgId,
        parentTeamId: team.id,
      },
    });

    // If the result team does not exist, throw an error
    if (!doesResultTeamExists) {
      this.logger.warn(
        `Result team ${createRoutingRuleDto.resultTeamId} not found`,
      );
      throw new NotFoundException("Result team not found!");
    }

    // Check if the AND or OR rules are provided
    if (!createRoutingRuleDto.andRules && !createRoutingRuleDto.orRules) {
      this.logger.warn("No rules provided for routing rule creation");
      throw new BadRequestException(
        "At least one set of rules are required to create a routing rule!",
      );
    }

    try {
      // Get the last rule
      const previousRules = await this.teamRoutingRulesRepository.findAll({
        where: { team: { id: team.id }, organization: { id: user.orgId } },
        order: { priority: "DESC" },
        take: 1,
      });

      // Get the last rule and its priority
      let lastRule: TeamRoutingRules;
      let lastRulePriority = 0;
      if (previousRules.length > 0) {
        lastRule = previousRules?.[0];
        lastRulePriority = lastRule?.priority;
      }

      this.logger.log(
        `Creating routing rule with priority ${lastRulePriority + 1}`,
      );
      // Create the routing rules in txn
      const rule = await this.transactionService.runInTransaction(
        async (txnContext) => {
          const ruleData: DeepPartial<TeamRoutingRules> = {
            name: createRoutingRuleDto.name,
            description: createRoutingRuleDto.description,
            team: { id: team.id },
            organization: { id: user.orgId },
            createdBy: { id: user.sub },
            andRules: createRoutingRuleDto.andRules,
            orRules: createRoutingRuleDto.orRules,
            priority:
              createRoutingRuleDto.evaluationOrder ?? lastRulePriority + 1,
            resultTeamId: createRoutingRuleDto.resultTeamId,
          };

          // Create the routing rule
          const routingRule = await this.teamRoutingRulesRepository.saveWithTxn(
            txnContext,
            ruleData,
          );

          return routingRule;
        },
      );

      // Get the returnable rule
      const returnableRule =
        await this.cachedTeamRoutingRulesRepository.findByCondition({
          where: { id: rule.id },
          relations: TEAM_ROUTING_RULES_RELATIONS,
        });

      // Invalidate and re-cache the team routing rules
      this.invalidateAndReCacheTeamRoutingRules(team.id, user.orgId);

      this.logger.log(`Successfully created routing rule for team ${teamId}`);
      return returnableRule;
    } catch (error) {
      this.logger.error(
        `Failed to create routing rule for team ${teamId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Updates a routing rule for a team.
   * @param teamId The team ID to update the routing rule for.
   * @param ruleId The rule ID to update.
   * @param user The user making the request.
   * @param updateRoutingRuleDto The routing rule data to update.
   * @returns The updated routing rule.
   */
  async updateRoutingRule(
    teamId: string,
    ruleId: string,
    user: CurrentUser,
    updateRoutingRuleDto: UpdateRoutingRuleGroupDto,
  ) {
    this.logger.log(
      `Updating routing rule ${ruleId} for team ${teamId} by user ${user.uid}`,
    );
    // Check if the user can update the team
    const { team } = await this.canUserUpdateTeam(teamId, user);

    // Check if the rule exists
    const rule = await this.teamRoutingRulesRepository.exists({
      where: {
        uid: ruleId,
        team: { id: team.id },
        organization: { id: user.orgId },
      },
    });

    // If the rule does not exist, throw an error
    if (!rule) {
      this.logger.warn(`Routing rule ${ruleId} not found`);
      throw new NotFoundException("Rule not found!");
    }

    // Check if the result team exists
    if (updateRoutingRuleDto.resultTeamId) {
      this.logger.log(
        `Validating result team ${updateRoutingRuleDto.resultTeamId}`,
      );
      const doesResultTeamExists = await this.teamRepository.exists({
        where: {
          uid: updateRoutingRuleDto.resultTeamId,
          organizationId: user.orgId,
          parentTeamId: team.id,
        },
      });

      // If the result team does not exist, throw an error
      if (!doesResultTeamExists) {
        this.logger.warn(
          `Result team ${updateRoutingRuleDto.resultTeamId} not found`,
        );
        throw new NotFoundException("Result team not found!");
      }
    }

    try {
      // Update the routing rule in a transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        const update: DeepPartial<TeamRoutingRules> = {};

        // Update the name
        if (updateRoutingRuleDto.name) {
          update.name = updateRoutingRuleDto.name;
        }

        // Update the description
        if (updateRoutingRuleDto.description) {
          update.description = updateRoutingRuleDto.description;
        }

        // Update the result team ID
        if (updateRoutingRuleDto.resultTeamId) {
          update.resultTeamId = updateRoutingRuleDto.resultTeamId;
        }

        // Update the evaluation order
        if (updateRoutingRuleDto.evaluationOrder) {
          update.priority = updateRoutingRuleDto.evaluationOrder;
        }

        // Update the AND rules
        if (updateRoutingRuleDto.andRules) {
          update.andRules = updateRoutingRuleDto.andRules;
        }

        // Update the OR rules
        if (updateRoutingRuleDto.orRules) {
          update.orRules = updateRoutingRuleDto.orRules;
        }

        this.logger.log(`Updating routing rule ${ruleId} properties`);
        // Update the routing rule
        await this.teamRoutingRulesRepository.updateWithTxn(
          txnContext,
          {
            uid: ruleId,
            team: { id: team.id },
            organization: { id: user.orgId },
          },
          update,
        );

        return update;
      });

      // Get the returnable rule
      const returnableRule =
        await this.teamRoutingRulesRepository.findByCondition({
          where: {
            uid: ruleId,
            organization: { id: user.orgId },
            team: { id: team.id },
          },
          relations: TEAM_ROUTING_RULES_RELATIONS,
        });

      // Invalidate and re-cache the team routing rules
      await this.invalidateAndReCacheTeamRoutingRules(team.id, user.orgId);

      this.logger.log(`Successfully updated routing rule ${ruleId}`);
      return returnableRule;
    } catch (error) {
      this.logger.error(`Failed to update routing rule ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Gets a team's routing rules.
   * @param teamId The team ID to get the routing rules of.
   * @param user The user making the request.
   * @returns The team's routing rules.
   */
  async getTeamRoutingRules(teamId: string, user: CurrentUser) {
    this.logger.log(`Fetching routing rules for team ${teamId}`);
    // Check if the user can update the team
    const { team } = await this.canUserUpdateTeam(teamId, user);

    // Get the team routing rules from cache
    const cacheKey = `team-routing-${team.id}-${user.orgId}`;
    const cachedRoutingRules = await this.cacheProvider.get<string>(cacheKey);

    // If the routing rules are cached, return them
    if (cachedRoutingRules) {
      this.logger.log(`Retrieved cached routing rules for team ${teamId}`);
      const rules = JSON.parse(cachedRoutingRules) as TeamRoutingRules[];
      return rules;
    }

    this.logger.log(`Fetching routing rules from database for team ${teamId}`);
    // If the routing rules are not cached, get them from the database
    const routingRules = await this.teamRoutingRulesRepository.findAll({
      where: { team: { id: team.id }, organization: { id: user.orgId } },
      relations: TEAM_ROUTING_RULES_RELATIONS,
    });

    try {
      this.logger.log(`Caching routing rules for team ${teamId}`);
      // Cache the routing rules
      await this.cacheProvider.set(
        cacheKey,
        JSON.stringify(routingRules),
        CACHE_TTL.MONTH * 3,
      );
    } catch (error) {
      this.logger.error(
        `Failed to cache team routing rules for team ${team.id}, error: ${error.message}`,
      );
    }

    // Return the routing rules
    return routingRules;
  }

  /**
   * Deletes a routing rule for a team.
   * @param teamId The team ID to delete the routing rule for.
   * @param ruleId The rule ID to delete.
   * @param user The user making the request.
   */
  async deleteRoutingRule(teamId: string, ruleId: string, user: CurrentUser) {
    this.logger.log(
      `Deleting routing rule ${ruleId} from team ${teamId} by user ${user.uid}`,
    );
    // Check if the user can update the team
    const { team } = await this.canUserUpdateTeam(teamId, user);

    // Check if the rule exists
    const rule = await this.teamRoutingRulesRepository.findByCondition({
      where: {
        uid: ruleId,
        team: { id: team.id },
        organization: { id: user.orgId },
      },
    });

    // If the rule does not exist, throw an error
    if (!rule) {
      this.logger.warn(`Routing rule ${ruleId} not found`);
      throw new NotFoundException("Rule not found!");
    }

    try {
      this.logger.log(`Removing routing rule ${ruleId}`);
      // Delete the rule
      await this.teamRoutingRulesRepository.remove(rule);

      // Invalidate and re-cache the team routing rules
      await this.invalidateAndReCacheTeamRoutingRules(team.id, user.orgId);
      this.logger.log(`Successfully deleted routing rule ${ruleId}`);
    } catch (error) {
      this.logger.error(`Failed to delete routing rule ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Invalidates and re-caches the team routing rules.
   * @param teamId The team ID to invalidate and re-cache the routing rules for.
   * @param orgId The organization ID to invalidate and re-cache the routing rules for.
   */
  private async invalidateAndReCacheTeamRoutingRules(
    teamId: string,
    orgId: string,
  ) {
    try {
      this.logger.log(
        `Invalidating and re-caching routing rules for team ${teamId}`,
      );
      // Generate the cache key
      const cacheKey = `team-routing-${teamId}-${orgId}`;

      // Purge the cache
      await this.cacheProvider.del(cacheKey);

      // Get the routing rules
      const routingRules = await this.teamRoutingRulesRepository.findAll({
        where: { team: { id: teamId }, organization: { id: orgId } },
        relations: TEAM_ROUTING_RULES_RELATIONS,
      });

      // Cache the routing rules
      await this.cacheProvider.set(
        cacheKey,
        JSON.stringify(routingRules),
        CACHE_TTL.WEEK * 2,
      );
      this.logger.log(
        `Successfully re-cached routing rules for team ${teamId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to purge and re-cache team routing rules for team ${teamId}, error: ${error.message}`,
      );
    }
  }

  /**
   * Generates a team identifier.
   * @returns The generated team identifier.
   */
  private generateTeamIdentifier(): string {
    const identifier = IdGeneratorUtils.generate("T");
    this.logger.log(`Generated team identifier: ${identifier}`);
    return identifier;
  }

  async checkTeamAvailability(
    currentUser: CurrentUser,
    teamId: string,
  ): Promise<TeamAvailabilityResponseDto> {
    // Find the team and validate it exists
    const team = await this.findOneByTeamId(teamId, currentUser.orgId);
    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    const currentTeamConfig = await this.getTeamConfigurations(
      team.uid,
      currentUser,
    );

    const { teamConfig: currentTeamConfigurations, businessHoursConfig } =
      currentTeamConfig;

    // Get team's timezone, defaulting to UTC if not set
    const teamTimezone = currentTeamConfigurations.timezone || "UTC";
    const now = DateTime.now().setZone(teamTimezone);

    // Check if today is a holiday
    const todayString = now.toFormat("dd-MM-yyyy");
    if (currentTeamConfigurations.holidays?.includes(todayString)) {
      return { isAvailable: false, reason: "HOLIDAY" };
    }

    // Check if team is available based on business hours
    const isTeamAvailable = this.sharedService.checkBusinessHoursAvailability(
      businessHoursConfig,
      teamTimezone,
    );

    if (!isTeamAvailable) {
      return { isAvailable: false, reason: "OUTSIDE_BUSINESS_HOURS" };
    }
    return { isAvailable: true, reason: "IN_BUSINESS_HOURS" };
  }
}
