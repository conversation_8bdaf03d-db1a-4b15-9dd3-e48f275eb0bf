import { BadRequestException, Injectable } from "@nestjs/common";
import Typesense from "typesense";
import { ConfigKeys, ConfigService } from "../../config/config.service";
import { AccountCollectionDto } from "../dto/account.collection.dto";
import { CommentCollectionDto } from "../dto/comment.collection.dto";
import { AccountSearchResponseDto } from "../dto/response/account-search-response.dto";
import { CommentSearchResponseDto } from "../dto/response/comment-search-response.dto";
import { TicketSearchResponseDto } from "../dto/response/ticket-search-response.dto";
import { SearchDocumentDto, TypesenseSearchResultDto } from "../dto/search.dto";
import { TicketCollectionDto } from "../dto/ticket.collection.dto";
import { ScopedKeyType } from "../enums/scoped-key-type.enum";
import { SearchCollection } from "../enums/search-collections.enum";
import { mapFieldsForTypesense } from "../utilities/typesense-field-mapping.util";
import { ALLOWED_QUERY_BY_FIELDS } from "./allowed-query-fields";
import { ApiKeySearchService } from "./api-key.search.service";

@Injectable()
export class SearchService {
  private client: any;

  constructor(
    private readonly configService: ConfigService,
    private readonly apiKeySearchService: ApiKeySearchService,
  ) {
    const typesenseHost = this.configService.get(ConfigKeys.TYPESENSE_HOST);
    const typesenseApiKey = this.configService.get(
      ConfigKeys.TYPESENSE_API_KEY,
    );
    let host: string = typesenseHost;
    const protocol: string = "https";
    let port: number = 443;
    if (host.startsWith("https://")) {
      const url = new URL(host);
      host = url.hostname;
      port = url.port ? parseInt(url.port) : 443;
    } else if (host.includes(":")) {
      const parts = host.split(":");
      host = parts[0];
      port = parseInt(parts[1]);
    }
    this.client = new Typesense.Client({
      nodes: [
        {
          host,
          port,
          protocol,
        },
      ],
      apiKey: typesenseApiKey,
      connectionTimeoutSeconds: 10,
    });
  }

  private getScopedKeyTypeForCollection(
    collection: string,
  ): ScopedKeyType | undefined {
    switch (collection) {
      case SearchCollection.TICKETS:
      case SearchCollection.COMMENTS:
        return ScopedKeyType.TEAM_AND_ORG;
      case SearchCollection.ACCOUNTS:
      case SearchCollection.CUSTOMER_CONTACTS:
        return ScopedKeyType.ORG_ONLY;
      default:
        return undefined;
    }
  }

  private mapHitsToDto(
    collection: string,
    result: any,
    teamIds?: string[],
    orgUid?: string,
  ): TypesenseSearchResultDto<SearchDocumentDto> {
    if (!result || !Array.isArray(result.hits)) return result;
    let dtoClass;
    switch (collection) {
      case SearchCollection.TICKETS:
        dtoClass = TicketSearchResponseDto;
        break;
      case SearchCollection.ACCOUNTS:
        dtoClass = AccountSearchResponseDto;
        break;
      case SearchCollection.COMMENTS:
        dtoClass = CommentSearchResponseDto;
        break;
      default:
        return result;
    }
    let hits = result.hits.map((hit) => ({
      //...hit, // This removes highlights array; we don't need it
      document: dtoClass.fromSearchResult(hit.document),
    }));

    /**
     * Below is the defensive filtering for team_uid/org_uid if present
     *
     * We need to make sure that the team_uid and org_uid are not null/undefined
     * and that the team_uid is in the list of team_uids
     * and that the org_uid is the same as the org_uid
     *
     * This is to prevent a user from seeing data that they should not be able to see
     *
     * EVEN THOUGH Typesense has a filter for team_uid and org_uid, we need to make sure that the data is correct
     */

    //Defensive filtering for team_uid/org_uid if present
    if (
      [SearchCollection.TICKETS, SearchCollection.COMMENTS].includes(
        collection as SearchCollection,
      ) &&
      teamIds &&
      orgUid
    ) {
      hits = hits.filter((hit) => {
        const doc = hit.document;
        // Drop if team_uid is null/undefined
        if (doc.teamUid == null) return false;
        const teamMatch = teamIds.includes(doc.teamUid);
        const orgMatch = doc.organizationUid && doc.organizationUid === orgUid;
        return teamMatch && orgMatch;
      });
    }
    // Defensive filtering for orgUid in accounts/customer_contacts
    if (
      [SearchCollection.ACCOUNTS, SearchCollection.CUSTOMER_CONTACTS].includes(
        collection as SearchCollection,
      ) &&
      orgUid
    ) {
      hits = hits.filter((hit) => {
        const doc = hit.document;
        // Drop if organization_uid is null/undefined
        if (doc.organizationUid == null) return false;
        return doc.organizationUid === orgUid;
      });
    }

    // from result remove the keys "out_of", "search_cutoff", "search_time_ms" if these keys exist
    if (result.out_of) delete result.out_of;
    if (result.search_cutoff) delete result.search_cutoff;
    if (result.search_time_ms) delete result.search_time_ms;

    return {
      ...result,
      hits,
    };
  }

  private validateQueryBy(collection: SearchCollection, query_by?: string) {
    if (!query_by) return true;
    const allowedFields = ALLOWED_QUERY_BY_FIELDS[collection];
    if (!allowedFields) return false;
    const fields = query_by.split(",").map((f) => f.trim());
    return fields.every((f) => allowedFields.includes(f));
  }

  private validateIncludeFields(
    collection: string,
    include_fields?: string,
  ): string {
    let allowedFields: string[] = [];
    switch (collection) {
      case SearchCollection.TICKETS:
        allowedFields = TicketCollectionDto.SELECT_FIELDS;
        break;
      case SearchCollection.ACCOUNTS:
        allowedFields = AccountCollectionDto.SELECT_FIELDS;
        break;
      case SearchCollection.COMMENTS:
        allowedFields = CommentCollectionDto.SELECT_FIELDS;
        break;
      default:
        throw new Error(`Unknown collection: ${collection}`);
    }

    const fields = (include_fields || "")
      .split(",")
      .map((f) => f.trim())
      .filter((f) => !!f);

    // Ensure team_uid and organization_uid are present
    if (!fields.includes("team_uid")) fields.push("team_uid");
    if (!fields.includes("organization_uid")) fields.push("organization_uid");

    for (const field of fields) {
      if (!allowedFields.includes(field)) {
        throw new Error(
          `Field '${field}' is not valid for collection '${collection}'`,
        );
      }
    }

    return fields.join(",");
  }

  async search(
    params: Record<string, any>,
    searchKey?: string,
    teamIds?: string[],
    orgUid?: string,
  ) {
    const {
      collection,
      query_by,
      include_fields,
      filter_by,
      sort_by,
      ...query
    } = params;

    const scopedKeyType = this.getScopedKeyTypeForCollection(collection);
    // Transform query_by, include_fields, filter_by, and sort_by from camelCase to snake_case
    const mappedFields = mapFieldsForTypesense(collection, {
      query_by,
      include_fields,
      filter_by,
      sort_by,
    });
    if (mappedFields.include_fields) {
      const validatedFields = this.validateIncludeFields(
        collection,
        mappedFields.include_fields,
      );
      query.include_fields = validatedFields;
    }
    if (mappedFields.query_by) {
      query.query_by = mappedFields.query_by;
    }
    if (mappedFields.filter_by) {
      query.filter_by = mappedFields.filter_by;
    }
    if (mappedFields.sort_by) {
      query.sort_by = mappedFields.sort_by;
    }

    if (!this.validateQueryBy(collection, query.query_by)) {
      throw new BadRequestException("Invalid query_by fields for collection");
    }

    if (scopedKeyType) {
      if (scopedKeyType === ScopedKeyType.TEAM_AND_ORG) {
        if (!teamIds || teamIds.length === 0 || !orgUid) {
          throw new Error("Missing teamUd(s) or organizationUid for search");
        }
      } else if (scopedKeyType === ScopedKeyType.ORG_ONLY) {
        if (!orgUid) {
          throw new Error("Missing organizationUid for search");
        }
      }
      const { result } = await this.apiKeySearchService.searchWithScopedKey(
        { collection, query_by, ...query },
        {
          type: scopedKeyType,
          teamUids: teamIds,
          organizationUid: orgUid,
        },
        searchKey,
      );
      const typedResult = this.mapHitsToDto(
        collection,
        result,
        teamIds,
        orgUid,
      );
      return {
        status: 200,
        data: { result: typedResult },
      };
    }
    throw new Error("Invalid or unsupported search collection");
  }

  async multiSearch(
    body: Record<string, any>,
    searchKey?: string,
    teamIds?: string[],
    orgUid?: string,
  ) {
    const results: any[] = [];
    const keys: Record<number, string> = {};
    if (Array.isArray(body.searches)) {
      for (let i = 0; i < body.searches.length; i++) {
        const search = body.searches[i];
        const { collection, query_by, ...query } = search;
        if (!this.validateQueryBy(collection, query_by)) {
          results.push({ error: "Invalid query_by fields for collection" });
          continue;
        }
        const scopedKeyType = this.getScopedKeyTypeForCollection(collection);
        if (scopedKeyType) {
          if (scopedKeyType === ScopedKeyType.TEAM_AND_ORG) {
            if (!teamIds || teamIds.length === 0 || !orgUid) {
              results.push({
                error:
                  "Missing team_uid(s) or organization_uid for scoped search",
              });
              continue;
            }
          } else if (scopedKeyType === ScopedKeyType.ORG_ONLY) {
            if (!orgUid) {
              results.push({
                error: "Missing organization_uid for scoped search",
              });
              continue;
            }
          }
          const { result } = await this.apiKeySearchService.searchWithScopedKey(
            { collection, ...query },
            {
              type: scopedKeyType,
              teamUids: teamIds,
              organizationUid: orgUid,
            },
            searchKey,
          );
          const typedResult = this.mapHitsToDto(
            collection,
            result,
            teamIds,
            orgUid,
          );
          results.push(typedResult);
          keys[i] = result.key;
        } else {
          // If we do not get a scopedKeyType, return an error for this search
          results.push({ error: "Invalid or unsupported search collection" });
        }
      }
      return { status: 200, data: { results, search_keys: keys } };
    } else {
      // Do not allow fallback to master key
      throw new Error(
        "Invalid multiSearch request: searches must be an array of search queries",
      );
    }
  }

  /**
   * Streams paginated search results, calling onPage for each page's DTO.
   * Calls onPage({ done: true }) at the end.
   */
  async streamSearch(
    params: Record<string, any>,
    searchKey: string | undefined,
    teamIds: string[] | undefined,
    orgUid: string | undefined,
    onPage: (data: any) => void | Promise<void>,
  ) {
    let page = 1;
    let totalPages = 1;
    let sentFirst = false;
    let found = 0;
    let perPage = params.per_page ? Number.parseInt(params.per_page, 10) : 10;
    if (perPage > 250) perPage = 250;
    try {
      while (page <= totalPages) {
        const pageQuery = {
          ...params,
          page: String(page),
          per_page: String(perPage),
        };
        const result = await this.search(pageQuery, searchKey, teamIds, orgUid);
        const data = result.data.result;
        await onPage(data);
        if (!sentFirst) {
          sentFirst = true;
          found = data.found;
          totalPages = Math.ceil(found / perPage);
        }
        page++;
      }
      await onPage({ done: true });
    } catch (err) {
      await onPage({ error: err.message || "Streaming error" });
    }
  }
}
