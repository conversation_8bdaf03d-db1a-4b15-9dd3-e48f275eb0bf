#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

pnpm pre-commit

# If any YAML files in the repo have changed, run the API reference generation script
if git diff --cached --name-only | grep -E '\.ya?ml$' > /dev/null; then
  echo "YAML changes detected. Running API reference generation..."
  bash apps/platform-docs/generate-api-reference.sh
  STATUS=$?
  if [ $STATUS -ne 0 ]; then
    echo "API reference generation failed. Aborting commit."
    exit 1
  fi
else
  echo "No YAML changes detected. Skipping API reference generation."
fi

# Check if there are changes in apps/web directory
if git diff --cached --name-only | grep -q "^apps/web/"; then
  echo "Changes detected in apps/web. Running tests..."
  cd apps/web && pnpm test && pnpm test:coverage
else
  echo "No changes in apps/web. Skipping tests."
fi
