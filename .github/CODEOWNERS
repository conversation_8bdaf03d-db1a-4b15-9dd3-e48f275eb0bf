# CODEOWNERS for Thena Backend Monorepo
# Specify code owners for each app or directory below.
# Example: @github-username or @org/team-name

/apps/agent-studio/ @aadarsh-gupta-thena @kartik-saxena-thena @San<PERSON>-jain-thena @amit-kumar-thena @harsh-thena @ankit-saxena
/apps/annotator/    @sudheer-bulusu-thena @amit-kumar-thena @harsh-thena @ankit-saxena
/apps/apps-platform/ @sudheer-bulusu-thena @amit-kumar-thena @harsh-thena @ankit-saxena
/apps/auth/         @sudheer-bulusu-thena @amit-kumar-thena @harsh-thena @ankit-saxena
/apps/email/        @sudheer-bulusu-thena @amit-kumar-thena @harsh-thena @ankit-saxena
/apps/email-ingester/ @sudheer-bulusu-thena @amit-kumar-thena @harsh-thena @ankit-saxena
/apps/platform/     @sudheer-bulusu-thena @amit-kumar-thena @harsh-thena @ankit-saxena
/apps/sla/          @sudheer-bulusu-thena @amit-kumar-thena @harsh-thena @ankit-saxena
/apps/web/          @aditi-thena @nishant-tiwari-thena @amit-kumar-thena @harsh-thena @ankit-saxena
/apps/workflows/    @sudheer-bulusu-thena @amit-kumar-thena @harsh-thena @ankit-saxena

# Add additional rules or update owners as needed.
# See https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners for details.
