name: Annotator Tests
on:
  pull_request:
    paths:
      - 'apps/annotator/**'
      - '.github/workflows/annotator-test-coverage.yml'
  # push:
  #   paths:
  #     - 'apps/annotator/**'
  #     - '.github/workflows/annotator-test-coverage.yml'
jobs:
  annotator-tests:
    runs-on: ubicloud-standard-4
    permissions:
      contents: write
      pages: write
      id-token: write
    
    # Enable colorful output
    env:
      FORCE_COLOR: 3
      
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10.1.0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.17'
          cache: 'pnpm'
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Configure npmrc for TipTap Pro
        run: |
          echo "//registry.npmjs.org/:_authToken=${{ secrets.TIPTAP_PRO_TOKEN }}" >> ~/.npmrc
      
      - name: Install dependencies
        env:
          TIPTAP_PRO_TOKEN: ${{ secrets.TIPTAP_PRO_TOKEN }}
        run: |
          cd $GITHUB_WORKSPACE
          pnpm install

      - name: Build Platform 
        run: |
          cd $GITHUB_WORKSPACE
          pnpm run build --filter=!@thena-backend/thena-web
      
      - name: Install Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest
      
      - name: Initialize Supabase
        run: supabase init
      - name: Start Supabase
        run: supabase db start
      
      - name: Reset test environment
        run: |
          cd $GITHUB_WORKSPACE

          # Run the command
          pnpm infra:test:reset
      
      - name: Get Auth Vault Secrets
        id: auth_vault
        run: |
          cd $GITHUB_WORKSPACE/apps/auth
          curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/test_coverage/auth| \
            jq -r '.data.data | to_entries | .[] | "export \(.key)=\(.value)"' > env_vars.sh
          chmod +x env_vars.sh
          source env_vars.sh
        continue-on-error: false
      
      - name: Start Auth Service
        run: |
          cd $GITHUB_WORKSPACE/apps/auth
          pnpm start:dev:test &
          echo $! > auth_pid.txt
      
      - name: Get Platform Vault Secrets
        id: platform_vault
        run: |
          cd $GITHUB_WORKSPACE/apps/apps-platform
          curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/test_coverage/apps-platform| \
            jq -r '.data.data | to_entries | .[] | "export \(.key)=\(.value)"' > env_vars.sh
          chmod +x env_vars.sh
          source env_vars.sh
        continue-on-error: false

      - name: Start Platform Service
        run: |
          cd $GITHUB_WORKSPACE/apps/apps-platform
          pnpm start:dev &
          echo $! > platform_pid.txt

      - name: Get Annotator Vault Secrets
        id: annotator_vault
        run: |
          cd $GITHUB_WORKSPACE/apps/annotator
          curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/test_coverage/annotator| \
            jq -r '.data.data | to_entries | .[] | "export \(.key)=\(.value)"' > env_vars.sh
          chmod +x env_vars.sh
          source env_vars.sh
        continue-on-error: false

      - name: Generate Badges
        run: |
          cd $GITHUB_WORKSPACE/apps/annotator
          pnpm run make-badges

      
      - name: Commit README changes
        if: github.ref == 'refs/heads/main'
        run: |
          git config user.name "GitHub Actions Bot"
          git config user.email "<EMAIL>"
          git add README.md
          git commit -m "Update coverage badges in README" || echo "No changes to commit"
          git push || echo "Failed to push changes"
      
      - name: Cleanup Auth Service
        if: always()
        run: |
          if [ -f "$GITHUB_WORKSPACE/apps/auth/auth_pid.txt" ]; then
            kill $(cat $GITHUB_WORKSPACE/apps/auth/auth_pid.txt) || true
          fi

      - name: Cleanup Platform Service
        if: always()
        run: |
          if [ -f "$GITHUB_WORKSPACE/apps/apps-platform/platform_pid.txt" ]; then
            kill $(cat $GITHUB_WORKSPACE/apps/apps-platform/platform_pid.txt) || true
          fi