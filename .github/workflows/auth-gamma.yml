name: Auth Gamma Deployment

on:
  workflow_dispatch: # Manual trigger only

env:
  AWS_REGION: us-east-1
  ECS_CLUSTER: thena-backend-gamma
  ECR_REPOSITORY: thena-platform
  SERVICE_NAME: thena-platform-auth
  TASK_DEFINITION: thena-platform-auth
  FORCE_COLOR: 3

jobs:
  deploy:
    runs-on: ubicloud-standard-2
    if: github.ref == 'refs/heads/main'
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_REMOTE_ONLY: true
      FORCE_COLOR: 3
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_GAMMA }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY_GAMMA }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID_GAMMA }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com
        run: |
          docker build -f apps/auth/Dockerfile \
            -t $ECR_REGISTRY/${{ env.ECR_REPOSITORY }}:thena-auth-${{ github.sha }} . --build-arg TURBO_TEAM=${{ env.TURBO_TEAM }} --build-arg TURBO_TOKEN=${{ env.TURBO_TOKEN }}
          docker push $ECR_REGISTRY/${{ env.ECR_REPOSITORY }}:thena-auth-${{ github.sha }}

      - name: Get Vault Secrets
        id: vault
        run: |
          VAULT_SECRETS=$(curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/auth-thena-backend-stagging | \
            jq -r '.data.data | @json')
          echo "secrets=${VAULT_SECRETS}" >> "$GITHUB_OUTPUT"

      - name: Update ECS service
        run: |
          # First, get the AWS account ID
            AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

            # Construct the image name properly
            FULL_IMAGE="${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/thena-platform:thena-auth-${{ github.sha }}"
            echo "DEBUG: Constructed FULL_IMAGE = $FULL_IMAGE"

            TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "thena-platform-auth" --include TAGS)

            # Modified jq command to preserve existing environment variables
            NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$FULL_IMAGE" \
            '.taskDefinition | 
            .containerDefinitions[0].image = $IMAGE |
            del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) |
            del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')

            # Continue only if NEW_TASK_DEFINITION is valid JSON
            if [ -z "$NEW_TASK_DEFINITION" ] || ! echo "$NEW_TASK_DEFINITION" | jq '.' >/dev/null 2>&1; then
                echo "ERROR: Failed to create valid task definition"
                exit 1
            fi

            # Register new task definition
            NEW_TASK_INFO=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEFINITION")
            NEW_REVISION=$(echo $NEW_TASK_INFO | jq '.taskDefinition.revision')

            # Debug print for NEW_TASK_INFO and NEW_REVISION
            echo "DEBUG: NEW_REVISION = $NEW_REVISION"

            aws ecs update-service \
                --cluster thena-backend-gamma \
                --service thena-platform-auth \
                --task-definition thena-platform-auth:${NEW_REVISION} \
                --force-new-deployment

            echo "Waiting for service deployment to complete..."
            aws ecs wait services-stable \
                --cluster thena-backend-gamma \
                --services thena-platform-auth
            echo "Service deployment completed successfully"
