name: Platform Gamma Deployment

on:
  workflow_dispatch: # Manual trigger only

env:
  AWS_REGION: us-east-1
  ECS_CLUSTER: thena-backend-gamma
  ECR_REPOSITORY: thena-platform
  SERVICE_NAME: thena-platform-new
  TASK_DEFINITION: platform
  FORCE_COLOR: 3

jobs:
  deploy:
    name: Deploy Platform Gamma
    runs-on: ubicloud-standard-2
    if: github.ref == 'refs/heads/main'
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_REMOTE_ONLY: true
      FORCE_COLOR: 3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_GAMMA }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY_GAMMA }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID_GAMMA }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com
        run: |
          docker build -f apps/platform/Dockerfile -t $ECR_REGISTRY/${{ env.ECR_REPOSITORY }}:thena-platform-${{ github.sha }} . --build-arg TURBO_TEAM=${{ env.TURBO_TEAM }} --build-arg TURBO_TOKEN=${{ env.TURBO_TOKEN }}
          docker push $ECR_REGISTRY/${{ env.ECR_REPOSITORY }}:thena-platform-${{ github.sha }}

      - name: Update ECS service
        run: |
          FULL_IMAGE="${{ secrets.AWS_ACCOUNT_ID_GAMMA }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOSITORY }}:thena-platform-${{ github.sha }}"

          TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "${{ env.TASK_DEFINITION }}" --include TAGS)

          NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$FULL_IMAGE" \
            '.taskDefinition | .containerDefinitions[0].image = $IMAGE | 
            del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | 
            del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')

          NEW_TASK_INFO=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEFINITION")
          NEW_REVISION=$(echo $NEW_TASK_INFO | jq '.taskDefinition.revision')

          aws ecs update-service \
            --cluster ${{ env.ECS_CLUSTER }} \
            --service ${{ env.SERVICE_NAME }} \
            --task-definition ${{ env.TASK_DEFINITION }}:${NEW_REVISION} \
            --force-new-deployment

          echo "Waiting for service deployment to complete..."
          aws ecs wait services-stable \
            --cluster ${{ env.ECS_CLUSTER }} \
            --services ${{ env.SERVICE_NAME }}

          echo "Service deployment completed successfully"
