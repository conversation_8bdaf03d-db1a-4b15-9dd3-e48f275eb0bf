name: Install Dependencies

on:
  push:
    branches: [main, production]

jobs:
  install_and_build:
    runs-on: ubicloud-standard-2
    env:
      FORCE_COLOR: 3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22.13.1"

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: Get pnpm store directory
        shell: bash
        env:
          FORCE_COLOR: 3
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: |
          pnpm config set store-dir .pnpm-store
          pnpm install
        env:
          FORCE_COLOR: 3
          TIPTAP_PRO_TOKEN: ${{ secrets.TIPTAP_PRO_TOKEN }}

      - name: Build
        run: pnpm build --filter=!@thena-backend/thena-web
        env:
          FORCE_COLOR: 3

      - name: Lint
        run: pnpm lint
        env:
          FORCE_COLOR: 3

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          path: |
            node_modules/
            .pnpm-store/
          retention-days: 1
