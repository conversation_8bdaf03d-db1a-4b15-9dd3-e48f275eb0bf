name: Platform Production Deployment

on:
  push:
    branches:
      - production
    paths:
      - 'apps/platform/**'
      - '.github/workflows/platform-production.yml'

env:
  AWS_REGION: us-east-1
  ECS_CLUSTER: thena-platform
  ECR_REPOSITORY: thena-platform
  SERVICE_NAME: thena-platform
  TASK_DEFINITION: thena-platform
  FORCE_COLOR: 3

jobs:
  deploy:
    runs-on: ubicloud-standard-2
    if: github.ref == 'refs/heads/production'
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_REMOTE_ONLY: true
      FORCE_COLOR: 3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_PLATFORM }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY_PLATFORM }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID_PLATFORM }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com
        run: |
          docker build -f apps/platform/Dockerfile \
            -t $ECR_REGISTRY/${{ env.ECR_REPOSITORY }}:thena-platform-${{ github.sha }} . --build-arg TURBO_TEAM=${{ env.TURBO_TEAM }} --build-arg TURBO_TOKEN=${{ env.TURBO_TOKEN }}
          docker push $ECR_REGISTRY/${{ env.ECR_REPOSITORY }}:thena-platform-${{ github.sha }}

      - name: Get Vault Secrets
        id: vault
        run: |
          curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/THENA_PLATFORM_PRODUCTION | \
            jq -r '.data.data' > vault_secrets.json

      - name: Update ECS service
        run: |
          FULL_IMAGE="${{ secrets.AWS_ACCOUNT_ID_PLATFORM }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOSITORY }}:thena-platform-${{ github.sha }}"

          TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "${{ env.TASK_DEFINITION }}" --include TAGS)

          NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$FULL_IMAGE" --slurpfile VAULT vault_secrets.json  \
            '.taskDefinition | .containerDefinitions[0].image = $IMAGE | 
            .containerDefinitions[0].environment = ($VAULT[0] | to_entries | map({name: .key, value: .value | tostring})) |
            del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | 
            del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')

          NEW_TASK_INFO=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEFINITION")
          NEW_REVISION=$(echo $NEW_TASK_INFO | jq '.taskDefinition.revision')

          aws ecs update-service \
            --cluster ${{ env.ECS_CLUSTER }} \
            --service ${{ env.SERVICE_NAME }} \
            --task-definition ${{ env.TASK_DEFINITION }}:${NEW_REVISION} \
            --force-new-deployment

          echo "Waiting for service deployment to complete..."
          aws ecs wait services-stable \
            --cluster ${{ env.ECS_CLUSTER }} \
            --services ${{ env.SERVICE_NAME }}

          echo "Service deployment completed successfully"
