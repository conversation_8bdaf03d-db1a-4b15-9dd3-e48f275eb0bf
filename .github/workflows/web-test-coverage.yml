name: Web Tests
on:
  pull_request:
    paths:
      - 'apps/web/**'
      - '.github/workflows/web-test-coverage.yml'
  push:
    paths:
      - 'apps/web/**'
      - '.github/workflows/web-test-coverage.yml'
jobs:
  web-tests:
    runs-on: ubicloud-standard-2
    permissions:
      contents: write
      pages: write
      id-token: write
    
    # Enable colorful output
    env:
      FORCE_COLOR: 3
      
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: Configure npmrc for TipTap Pro
        run: |
          echo "//registry.npmjs.org/:_authToken=${{ secrets.TIPTAP_PRO_TOKEN }}" >> ~/.npmrc
      
      - name: Install dependencies
        env:
          TIPTAP_PRO_TOKEN: ${{ secrets.TIPTAP_PRO_TOKEN }}
        run: |
          cd $GITHUB_WORKSPACE/apps/web
          pnpm install
      
      - name: Run tests with coverage
        env:
          FORCE_COLOR: 3
          CI: true
        run: |
          cd $GITHUB_WORKSPACE/apps/web
          pnpm test
          
          # Debug - list the coverage directory contents
          echo "Contents of coverage directory:"
          ls -la coverage/ || echo "No coverage directory found"
      
      - name: Generate coverage badge
        run: |
          cd $GITHUB_WORKSPACE/apps/web
          
          # Check if coverage directory exists
          if [ ! -d "coverage" ]; then
            echo "Coverage directory not found, creating it"
            mkdir -p coverage
          fi
          
          # Check if coverage summary exists
          if [ ! -f "coverage/coverage-summary.json" ]; then
            echo "Coverage summary not found, creating a placeholder"
            echo '{"total":{"lines":{"total":0,"covered":0,"skipped":0,"pct":0},"statements":{"total":0,"covered":0,"skipped":0,"pct":0},"functions":{"total":0,"covered":0,"skipped":0,"pct":0},"branches":{"total":0,"covered":0,"skipped":0,"pct":0}}}' > coverage/coverage-summary.json
          fi
          
          # Get current coverage percentage
          COVERAGE=$(jq '.total.statements.pct' coverage/coverage-summary.json)
          
          # Determine badge color based on coverage percentage
          if (( $(echo "$COVERAGE < 70" | bc -l) )); then
            COLOR="red"
          elif (( $(echo "$COVERAGE < 80" | bc -l) )); then
            COLOR="yellow"
          else
            COLOR="brightgreen"
          fi
          
          # Generate badge using shields.io and save as SVG
          curl -s "https://img.shields.io/badge/coverage-${COVERAGE}%25-${COLOR}" > coverage/coverage-badge.svg
          
          # Create the badges directory in the repository if it doesn't exist
          mkdir -p $GITHUB_WORKSPACE/.github/badges
          
          # Copy the badge to the repository
          cp coverage/coverage-badge.svg $GITHUB_WORKSPACE/.github/badges/web-coverage-badge.svg
      
      - name: Update README with coverage badge
        run: |
          # Use absolute GitHub URL for the badge
          BADGE_URL="https://raw.githubusercontent.com/thena-ai/thena-platform/main/.github/badges/web-coverage-badge.svg"
          
          # Check if README exists at the root
          if [ ! -f "README.md" ]; then
            echo "# Project" > README.md
            echo "Creating new README.md at root"
          fi
          
          # Check if README already has Web Tests Coverage badge
          if grep -q "!\[Web Tests Coverage\]" README.md; then
            # Replace existing badge
            echo "Updating existing Web Tests Coverage badge in README"
            sed -i "s|!\[Web Tests Coverage\]([^)]*)|![Web Tests Coverage](${BADGE_URL})|g" README.md
          elif grep -q "\[\[Web Tests Coverage\]\]" README.md; then
            # Replace markdown link format
            echo "Updating existing Web Tests Coverage badge link in README"
            sed -i "s|\[\[Web Tests Coverage\]\]([^)]*)|[![Web Tests Coverage](${BADGE_URL})](https://github.com/thena-ai/thena-platform/actions/workflows/web-test-coverage.yml)|g" README.md
          else
            # Add new badge 
            echo "Adding new Web Tests Coverage badge to README"
            if grep -q "Coverage" README.md; then
              # Add after the last existing Coverage badge
              sed -i '/Coverage/ s/$/ [![Web Tests Coverage]('"${BADGE_URL}"')](https://github.com/thena-ai/thena-platform/actions/workflows/web-test-coverage.yml)/' README.md
            else
              # Add under the title
              sed -i '1,/^# /!b; /^# /a\\n[![Web Tests Coverage]('"${BADGE_URL}"')](https://github.com/thena-ai/thena-platform/actions/workflows/web-test-coverage.yml)\n' README.md
            fi
          fi
          
          cat README.md
      
      - name: Commit README changes and badge
        if: github.ref == 'refs/heads/main'
        run: |
          git config user.name "GitHub Actions Bot"
          git config user.email "<EMAIL>"
          git add README.md .github/badges/web-coverage-badge.svg
          git commit -m "Update web test coverage badge in README" || echo "No changes to commit"
          git push || echo "Failed to push changes"