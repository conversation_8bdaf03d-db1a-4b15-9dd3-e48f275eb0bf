name: Agent Studio Tests
on:
  pull_request:
    branches:
      - main
    paths:
      - 'apps/agent-studio/**'
      - '.github/workflows/agent-studio-test-coverage.yml'
  push:
    branches:
      - main
    paths:
      - 'apps/agent-studio/**'
      - '.github/workflows/agent-studio-test-coverage.yml'
jobs:
  agent-studio-tests:
    runs-on: ubicloud-standard-4
    permissions:
      contents: write
      pages: write
      id-token: write
    
    # Enable colorful output
    env:
      FORCE_COLOR: 3
      
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Set up Python 3.11.11
        uses: actions/setup-python@v5
        with:
          python-version: '3.11.11'
          cache: 'pip'
      
      - name: Install dependencies
        env:
          FORCE_COLOR: 3
        run: |
          python -m pip install --upgrade pip
          pip install uv pytest pytest-xdist pytest-cov
      
      - name: Setup Python environment
        env:
          FORCE_COLOR: 3
        run: |
          # Navigate to agent-studio directory
          cd $GITHUB_WORKSPACE/apps/agent-studio
          
          # Create virtual environment and install dependencies
          uv venv --python=3.11.11 .venv
          source .venv/bin/activate
          
          # Install dependencies using uv
          uv pip compile pyproject.toml -o requirements.lock
          uv pip sync requirements.lock
          
          # Install additional packages for testing
          uv pip install pytest pytest-xdist pytest-cov
      
      - name: Get Vault Secrets
        id: vault
        run: |
          cd $GITHUB_WORKSPACE/apps/agent-studio
          curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/THENA_AGENT_STUDIO_TEST_COVERAGE | \
            jq -r '.data.data | to_entries | .[] | "export \(.key)=\(.value)"' > env_vars.sh
          chmod +x env_vars.sh
        continue-on-error: false

      - name: Run tests
        env:
          FORCE_COLOR: 3
          PYTEST_ADDOPTS: "--color=yes"
        run: |
          cd $GITHUB_WORKSPACE/apps/agent-studio
          
          # Source environment variables
          if [ -f "env_vars.sh" ]; then
            source env_vars.sh
          else
            echo "ERROR: env_vars.sh not found, tests may fail"
            exit 1
          fi
          
          # Activate virtual environment
          source .venv/bin/activate
          
          # Run tests with coverage
          python -m pytest tests -v -n 10 --cov=. --cov-report=json --cov-report=term
          
          # Make sure coverage directory exists
          mkdir -p $GITHUB_WORKSPACE/coverage
          
          # Copy coverage file to the expected location for the badge action
          if [ -f "coverage.json" ]; then
            cp coverage.json $GITHUB_WORKSPACE/coverage/coverage-summary.json
          fi
          
      # - name: Setup Pages
      #   if: github.ref == format('refs/heads/{0}', github.event.repository.default_branch)
      #   run: |
      #     # Check if gh-pages branch exists
      #     if ! git ls-remote --heads origin gh-pages | grep gh-pages; then
      #       echo "Creating gh-pages branch"
      #       git config --global user.name "GitHub Actions"
      #       git config --global user.email "<EMAIL>"
      #       git checkout --orphan gh-pages
      #       git reset --hard
      #       git commit --allow-empty -m "Initial commit"
      #       git push origin gh-pages
      #       git checkout -
      #     else
      #       echo "gh-pages branch already exists"
      #     fi
      

      # - name: Update Coverage Badge
      #   # For this to work:
      #   # 1. Ensure you have a gh-pages branch
      #   # 2. Enable GitHub Pages in repository settings
      #   # 3. Set "Read and write permissions" in repository Actions settings
      #   if: github.ref == format('refs/heads/{0}', github.event.repository.default_branch)
      #   uses: we-cli/coverage-badge-action@main
      #   with:
      #     coverage-path: coverage/coverage-summary.json
      #     fallback-path: apps/agent-studio/coverage.json

