openapi: 3.0.0
paths:
  /v1/slack/install/{orgId}/{userId}:
    get:
      operationId: installForOrganization
      parameters:
        - name: orgId
          required: true
          in: path
          schema:
            type: string
          description: ID of the orgId
          example: id123
        - name: userId
          required: true
          in: path
          schema:
            type: string
          description: ID of the userId
          example: U12345678
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_0
        - Slack
      description: Retrieve install
      summary: Retrieve install
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/workspaces:
    get:
      operationId: getWorkspaces
      parameters:
        - name: __sa_k
          required: true
          in: path
          schema:
            type: string
          description: ID of the __sa_k
          example: example-__sa_k
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_0
      description: Retrieve workspaces
      summary: Retrieve workspaces
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/workspaces/disconnect:
    delete:
      operationId: disconnectWorkspace
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_0
      description: Delete disconnect
      summary: Delete disconnect
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/settings/teams/{teamId}:
    get:
      description: Retrieves all settings for a specific team
      operationId: getTeamSettings
      parameters:
        - name: teamId
          required: true
          in: path
          description: Platform team ID
          schema:
            type: string
          example: T12345678
      responses:
        '200':
          description: Team settings retrieved successfully
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Team not found
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_1
        - x-api-key: []
        - bearer: []
      summary: Get team settings
      tags: &ref_2
        - Settings
    put:
      description: Updates settings for a specific team
      operationId: updateSettings
      parameters:
        - name: teamId
          required: true
          in: path
          description: Platform team ID
          schema:
            type: string
          example: T12345678
      requestBody:
        required: true
        description: Settings to update
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettingsDTO'
      responses:
        '200':
          description: Settings updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Team not found
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_1
      summary: Update team settings
      tags: *ref_2
  /v1/slack/settings/teams/{teamId}/settings/{key}:
    delete:
      description: Deletes a specific setting for a team
      operationId: deleteSetting
      parameters:
        - name: teamId
          required: true
          in: path
          description: Platform team ID
          schema:
            type: string
          example: T12345678
        - name: key
          required: true
          in: path
          description: Setting key to delete
          schema:
            type: string
          example: example-key
      responses:
        '200':
          description: Setting deleted successfully
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Setting or team not found
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_1
      summary: Delete a setting
      tags: *ref_2
  /v1/slack/settings/teams/{teamId}/reset:
    post:
      description: Resets all settings for a team to default values
      operationId: resetSettings
      parameters:
        - name: teamId
          required: true
          in: path
          description: Platform team ID
          schema:
            type: string
          example: T12345678
      responses:
        '201':
          description: Settings reset successfully
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Team not found
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_1
      summary: Reset team settings
      tags: *ref_2
  /v1/slack/sub-groups:
    get:
      operationId: getAllSubGroupsForWorkspace
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_3
        - SlackSubGroups
      description: Retrieve sub-groups
      summary: Retrieve sub-groups
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    patch:
      operationId: createMapping
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MapSubGroupToSubTeamDTO'
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_3
      description: Partially update sub-groups
      summary: Partially update sub-groups
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/sub-groups/mappings:
    get:
      operationId: getAllSubGroupMappings
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_3
      description: Retrieve mappings
      summary: Retrieve mappings
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/sub-groups/mappings/{id}:
    patch:
      operationId: updateMapping
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
          description: ID of the id
          example: id123
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSubGroupMappingDTO'
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_3
      description: Partially update mappings
      summary: Partially update mappings
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    delete:
      operationId: deleteMapping
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
          description: ID of the id
          example: id123
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_3
      description: Delete mappings
      summary: Delete mappings
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/sub-groups/mapped-teams:
    get:
      operationId: getAllMappedSubGroupsAndTeams
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_3
      description: Retrieve mapped-teams
      summary: Retrieve mapped-teams
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/triage/{teamId}:
    post:
      operationId: createRule
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
          description: ID of the teamId
          example: T12345678
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTriageRuleDto'
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_4
        - TriageRules
      description: Create triage
      summary: Create triage
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    get:
      operationId: getRules
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
          description: ID of the teamId
          example: T12345678
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_4
      description: Retrieve triage
      summary: Retrieve triage
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/triage/{ruleId}:
    patch:
      operationId: updateRule
      parameters:
        - name: ruleId
          required: true
          in: path
          schema:
            type: string
          description: ID of the ruleId
          example: id123
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTriageRuleDto'
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_4
      description: Partially update triage
      summary: Partially update triage
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    delete:
      operationId: deleteRule
      parameters:
        - name: ruleId
          required: true
          in: path
          schema:
            type: string
          description: ID of the ruleId
          example: id123
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_4
      description: Delete triage
      summary: Delete triage
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/teams:
    get:
      operationId: getAllTeams
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_5
        - Teams
      description: Retrieve teams
      summary: Retrieve teams
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    post:
      operationId: addTeam
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddTeamDTO'
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_5
      description: Create teams
      summary: Create teams
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/teams/map-channels:
    post:
      operationId: mapTeamToChannels
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MapTeamToChannelsDTO'
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_5
      description: Create map-channels
      summary: Create map-channels
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/teams/map-secondary-channels:
    post:
      operationId: mapTeamToSecondaryChannels
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MapTeamToSecondaryChannelsDTO'
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_5
      description: Create map-secondary-channels
      summary: Create map-secondary-channels
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/teams/disconnect-channels:
    delete:
      operationId: disconnectTeamToChannels
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisconnectTeamToChannelsDTO'
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_5
      description: Delete disconnect-channels
      summary: Delete disconnect-channels
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/teams/{teamId}:
    get:
      operationId: getPlatformTeam
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
          description: ID of the teamId
          example: T12345678
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_5
      description: Retrieve teams
      summary: Retrieve teams
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/teams/disconnect/{teamId}:
    delete:
      operationId: disconnectTeam
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
          description: ID of the teamId
          example: T12345678
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_5
      description: Delete disconnect
      summary: Delete disconnect
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/interactions:
    post:
      operationId: handleInteraction
      parameters: []
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
        - SlackInteractions
      description: Create interactions
      summary: Create interactions
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/channel/{teamId}:
    get:
      operationId: getAllSlackChannelsByTeamId
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
          description: ID of the teamId
          example: T12345678
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_6
        - SlackChannel
      description: Retrieve channel
      summary: Retrieve channel
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/channel:
    get:
      operationId: getAllSlackChannels
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_6
      description: Retrieve channel
      summary: Retrieve channel
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/channel/configure-triage-channel:
    post:
      operationId: configureTriageChannel
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigureTriageChannelDTO'
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_6
      description: Create configure-triage-channel
      summary: Create configure-triage-channel
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/channel/thread/{channelId}:
    post:
      operationId: createTriageThread
      parameters:
        - name: channelId
          required: true
          in: path
          schema:
            type: string
          description: ID of the channelId
          example: C12345678
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTriageThreadDTO'
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_6
      description: Create thread
      summary: Create thread
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/activities/post-message:
    post:
      description: Sends a message to a Slack channel
      operationId: postMessage
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostMessageDTO'
      responses:
        '200':
          description: Message posted successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PostMessageResponseDTO'
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Error posting message
      security: &ref_7
        - x-api-key: []
      summary: Post a message to Slack
      tags: &ref_8
        - SlackActivities
  /v1/slack/activities/update-message:
    post:
      description: Updates an existing message in a Slack channel
      operationId: updateMessage
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMessageDTO'
      responses:
        '200':
          description: Message updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateMessageResponseDTO'
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Error updating message
      security: *ref_7
      summary: Update a Slack message
      tags: *ref_8
  /v1/slack/activities/delete-message:
    post:
      description: Deletes a message from a Slack channel
      operationId: deleteMessage
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteMessageDTO'
      responses:
        '200':
          description: Message deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteMessageResponseDTO'
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Error deleting message
      security: *ref_7
      summary: Delete a Slack message
      tags: *ref_8
  /v1/slack/activities/add-reaction:
    post:
      description: Adds an emoji reaction to a Slack message
      operationId: addReaction
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddReactionDTO'
      responses:
        '200':
          description: Reaction added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddReactionResponseDTO'
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Error adding reaction
      security: *ref_7
      summary: Add a reaction to a message
      tags: *ref_8
  /v1/slack/activities/remove-reaction:
    post:
      description: Removes an emoji reaction from a Slack message
      operationId: removeReaction
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RemoveReactionDTO'
      responses:
        '200':
          description: Reaction removed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoveReactionResponseDTO'
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Error removing reaction
      security: *ref_7
      summary: Remove a reaction from a message
      tags: *ref_8
  /v1/slack/activities/invite-user-to-conversation:
    post:
      description: Invites a user to a Slack channel or group
      operationId: inviteUserToConversation
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InviteUserToConversationDTO'
      responses:
        '200':
          description: User invited successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InviteUserToConversationResponseDTO'
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Error inviting user
      security: *ref_7
      summary: Invite a user to a conversation
      tags: *ref_8
  /v1/slack/activities/kick-user-from-conversation:
    post:
      description: Removes a user from a Slack channel or group
      operationId: kickUserFromConversation
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KickUserFromConversationDTO'
      responses:
        '200':
          description: User removed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KickUserFromConversationResponseDTO'
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Error removing user
      security: *ref_7
      summary: Remove a user from a conversation
      tags: *ref_8
  /v1/slack/activities/join-conversation:
    post:
      description: Joins a Slack channel
      operationId: joinConversation
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JoinConversationDTO'
      responses:
        '200':
          description: Joined conversation successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JoinConversationResponseDTO'
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Error joining conversation
      security: *ref_7
      summary: Join a conversation
      tags: *ref_8
  /v1/slack/activities/leave-conversation:
    post:
      description: Leaves a Slack channel
      operationId: leaveConversation
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LeaveConversationDTO'
      responses:
        '200':
          description: Left conversation successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeaveConversationResponseDTO'
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Error leaving conversation
      security: *ref_7
      summary: Leave a conversation
      tags: *ref_8
  /v1/prompts:
    get:
      operationId: getPrompts
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_9
        - Prompts
      description: Retrieve prompts
      summary: Retrieve prompts
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    post:
      operationId: createPrompt
      parameters: []
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_9
      description: Create prompts
      summary: Create prompts
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/prompts/{id}:
    get:
      operationId: getPromptById
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
          description: ID of the id
          example: id123
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_9
      description: Retrieve prompts
      summary: Retrieve prompts
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    put:
      operationId: updatePrompt
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
          description: ID of the id
          example: id123
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_9
      description: Update prompts
      summary: Update prompts
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    delete:
      operationId: deletePrompt
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
          description: ID of the id
          example: id123
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_9
      description: Delete prompts
      summary: Delete prompts
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/prompts/{id}/set-default/{teamId}:
    post:
      operationId: setDefaultPrompt
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
          description: ID of the id
          example: id123
        - name: teamId
          required: true
          in: path
          schema:
            type: string
          description: ID of the teamId
          example: T12345678
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_9
      description: Create set-default
      summary: Create set-default
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/prompts/seed/{teamId}:
    post:
      operationId: seedDefaultPrompts
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
          description: ID of the teamId
          example: T12345678
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_9
      description: Create seed
      summary: Create seed
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/prompts/seed-all:
    post:
      operationId: seedDefaultPromptsForAllTeams
      parameters: []
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_9
      description: Create seed-all
      summary: Create seed-all
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/prompts/teams/{teamId}:
    get:
      operationId: getPromptsByTeamId
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
          description: ID of the teamId
          example: T12345678
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_9
      description: Retrieve teams
      summary: Retrieve teams
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/authorization/user:
    get:
      operationId: getAuthorizeUserURL
      parameters:
        - name: userEmail
          required: true
          in: query
          schema:
            type: string
          description: Filter results by userEmail
          example: example-userEmail
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_10
        - Authorization
      description: Retrieve user
      summary: Retrieve user
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    delete:
      operationId: deleteUserAuthorization
      parameters:
        - name: userEmail
          required: true
          in: query
          schema:
            type: string
          description: Filter results by userEmail
          example: example-userEmail
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_10
      description: Delete user
      summary: Delete user
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/authorization/user/callback:
    get:
      operationId: authorizeUser
      parameters:
        - name: code
          required: true
          in: query
          schema:
            type: string
          description: Filter results by code
          example: example-code
        - name: state
          required: true
          in: query
          schema:
            type: string
          description: Filter results by state
          example: example-state
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_10
      description: Retrieve callback
      summary: Retrieve callback
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/slack/form-builder/select-form:
    post:
      description: Processes a form selection from Slack and opens a modal with the selected form
      operationId: handleFormSelection
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FormSelectionDTO'
      responses:
        '200':
          description: Form selection processed successfully
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Invalid payload format
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
      security: &ref_11
        - x-api-key: []
      summary: Handle form selection
      tags: &ref_12
        - FormBuilder
  /v1/slack/form-builder/submit-form:
    post:
      description: Processes a form submission from Slack and sends the data to the appropriate destination
      operationId: handleFormSubmission
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FormSubmissionDTO'
      responses:
        '200':
          description: Form submission processed successfully
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Invalid payload format
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
      security: *ref_11
      summary: Handle form submission
      tags: *ref_12
  /v1/slack/form-builder/interact:
    post:
      description: Processes interactions with form fields and updates the form based on conditional logic
      operationId: handleInteraction
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FormInteractionDTO'
      responses:
        '200':
          description: Interaction processed successfully
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Invalid payload format
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
      security: *ref_11
      summary: Handle form interactions
      tags: *ref_12
  /v1/slack/sync/{syncType}:
    post:
      operationId: sync
      parameters:
        - name: syncType
          required: true
          in: path
          schema:
            type: string
          description: ID of the syncType
          example: example-syncType
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
        - SlackSync
      description: Create sync
      summary: Create sync
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/platform/installations:
    head:
      operationId: checkInstallationsHTTPResource
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_13
        - Platform
      description: Perform operation on installations
      summary: Perform operation on installations
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    post:
      operationId: handleInstallation
      parameters: []
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_13
      description: Create installations
      summary: Create installations
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/platform/events:
    head:
      operationId: checkEventsHTTPResource
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_13
      description: Perform operation on events
      summary: Perform operation on events
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
    post:
      operationId: handleEvents
      parameters: []
      responses:
        '201':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_13
      description: Create events
      summary: Create events
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/ai/providers:
    get:
      operationId: getAllProviders
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_14
        - Ai
      description: Retrieve providers
      summary: Retrieve providers
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/ai/models:
    get:
      operationId: getModels
      parameters:
        - name: provider
          required: true
          in: path
          schema:
            type: string
          description: ID of the provider
          example: id123
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_14
      description: Retrieve models
      summary: Retrieve models
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/ai/health:
    get:
      operationId: checkAIHealth
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_14
      description: Retrieve health
      summary: Retrieve health
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /v1/ai/health/openai:
    get:
      operationId: checkOpenAIHealth
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_14
      description: Retrieve openai
      summary: Retrieve openai
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /:
    get:
      operationId: getHello
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: &ref_15
        - App
      description: Retrieve resource
      summary: Retrieve resource
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
  /health:
    get:
      operationId: health
      parameters: []
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Resource does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags: *ref_15
      description: Retrieve health
      summary: Retrieve health
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
info:
  title: Thena Slack API
  description: API documentation for Thena Slack integration
  version: '1.0'
  contact: {}
tags:
  - name: FormBuilder
    description: Form builder and submission endpoints
  - name: SlackActivities
    description: Slack activities and interactions endpoints
  - name: Members
    description: Slack workspace members endpoints
  - name: Teams
    description: Slack teams and workspaces endpoints
  - name: Settings
    description: Application settings endpoints
  - name: Authorization
    description: Authentication and authorization endpoints
  - name: SlackSync
    description: Slack data synchronization endpoints
  - name: Ai
    description: AI provider and model endpoints
  - name: Platform
    description: Platform integration endpoints
  - name: SubGroups
    description: Slack subgroups management endpoints
  - name: TriageRules
    description: Triage rules management endpoints
servers: []
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-KEY
      description: API key for authentication. Must be included in the X-API-KEY header for all requests.
  schemas:
    UpdateSettingsDTO:
      type: object
      properties:
        conversationWindow:
          type: number
          description: Time window in minutes for conversation context
          minimum: 0
          maximum: 1440
          example: 60
        automaticTickets:
          type: boolean
          description: Whether to automatically create tickets
          example: true
        slashCommands:
          type: boolean
          description: Whether to enable slash commands
          example: true
        requireForm:
          type: boolean
          description: Whether to require form submission
          example: false
        thenaBotTaggingEnabled:
          type: boolean
          description: Whether to enable Thena bot tagging
          example: true
        ticketCommand:
          type: boolean
          description: Whether to enable ticket command
          example: true
        enableTicketCreationViaReaction:
          type: boolean
          description: Whether to enable ticket creation via reaction
          example: true
        aiEnableExtendedThinking:
          type: boolean
          description: Whether to enable extended AI thinking
          example: true
        aiModel:
          type: string
          description: AI model to use
          example: gpt-4
        aiTemperature:
          type: number
          description: AI temperature setting
          example: 0.7
        aiMaxTokens:
          type: number
          description: Maximum tokens for AI responses
          example: 2048
        selectedForms:
          type: array
          description: Selected forms for the team
          items:
            type: string
          example:
            - form1
            - form2
      description: DTO for updating team settings
    UpdateSubGroupMappingDTO:
      type: object
      properties: {}
    MapSubGroupToSubTeamDTO:
      type: object
      properties: {}
    CreateTriageRuleDto:
      type: object
      properties: {}
    UpdateTriageRuleDto:
      type: object
      properties: {}
    AddTeamDTO:
      type: object
      properties: {}
    MapTeamToChannelsDTO:
      type: object
      properties: {}
    MapTeamToSecondaryChannelsDTO:
      type: object
      properties: {}
    DisconnectTeamToChannelsDTO:
      type: object
      properties: {}
    ConfigureTriageChannelDTO:
      type: object
      properties: {}
    CreateTriageThreadDTO:
      type: object
      properties: {}
    PostMessageDTO:
      type: object
      properties:
        channel:
          type: string
          description: The Slack channel ID to post the message to
          example: C12345678
        text:
          type: string
          description: The text content of the message
          example: Hello world!
        threadTs:
          type: string
          description: The timestamp of the thread to reply to
          example: '1234567890.123456'
        blocks:
          type: object
          description: Slack Block Kit blocks for rich message formatting
          example:
            - type: section
              text:
                type: mrkdwn
                text: '*Hello* world!'
        unfurlLinks:
          type: boolean
          description: Whether to unfurl links in the message
          example: true
          default: true
        unfurlMedia:
          type: boolean
          description: Whether to unfurl media in the message
          example: true
          default: true
      required:
        - channel
        - text
    PostMessageResponseDTO:
      type: object
      properties:
        ok:
          type: boolean
          description: Whether the operation was successful
          example: true
        channel:
          type: string
          description: The channel ID where the message was posted
          example: C12345678
        ts:
          type: string
          description: The timestamp of the message
          example: '1234567890.123456'
        message:
          type: object
          description: The message object
          example:
            text: Hello world!
      required:
        - ok
        - channel
        - ts
        - message
    UpdateMessageDTO:
      type: object
      properties:
        channel:
          type: string
          description: The Slack channel ID where the message is located
          example: C12345678
        text:
          type: string
          description: The new text content for the message
          example: Updated message text
        ts:
          type: string
          description: The timestamp of the message to update
          example: '1234567890.123456'
        blocks:
          type: string
          description: Slack Block Kit blocks for rich message formatting
          example: '[{"type":"section","text":{"type":"mrkdwn","text":"*Updated* message!"}}]'
      required:
        - channel
        - text
        - ts
    UpdateMessageResponseDTO:
      type: object
      properties: {}
    DeleteMessageDTO:
      type: object
      properties:
        channel:
          type: string
          description: The Slack channel ID where the message is located
          example: C12345678
        ts:
          type: string
          description: The timestamp of the message to delete
          example: '1234567890.123456'
      required:
        - channel
        - ts
    DeleteMessageResponseDTO:
      type: object
      properties: {}
    AddReactionDTO:
      type: object
      properties:
        channel:
          type: string
          description: The Slack channel ID where the message is located
          example: C12345678
        ts:
          type: string
          description: The timestamp of the message to add a reaction to
          example: '1234567890.123456'
        name:
          type: string
          description: The name of the emoji reaction to add
          example: thumbsup
      required:
        - channel
        - ts
        - name
    AddReactionResponseDTO:
      type: object
      properties: {}
    RemoveReactionDTO:
      type: object
      properties:
        channel:
          type: string
          description: The Slack channel ID where the message is located
          example: C12345678
        ts:
          type: string
          description: The timestamp of the message to remove a reaction from
          example: '1234567890.123456'
        name:
          type: string
          description: The name of the emoji reaction to remove
          example: thumbsup
      required:
        - channel
        - ts
        - name
    RemoveReactionResponseDTO:
      type: object
      properties: {}
    InviteUserToConversationDTO:
      type: object
      properties:
        channel:
          type: string
          description: The Slack channel ID to invite the user to
          example: C12345678
        user:
          type: string
          description: The Slack user ID to invite
          example: U12345678
      required:
        - channel
        - user
    InviteUserToConversationResponseDTO:
      type: object
      properties: {}
    KickUserFromConversationDTO:
      type: object
      properties:
        channel:
          type: string
          description: The Slack channel ID to remove the user from
          example: C12345678
        user:
          type: string
          description: The Slack user ID to remove from the channel
          example: U12345678
      required:
        - channel
        - user
    KickUserFromConversationResponseDTO:
      type: object
      properties: {}
    JoinConversationDTO:
      type: object
      properties:
        channel:
          type: string
          description: The Slack channel ID to join
          example: C12345678
      required:
        - channel
    JoinConversationResponseDTO:
      type: object
      properties: {}
    LeaveConversationDTO:
      type: object
      properties:
        channel:
          type: string
          description: The Slack channel ID to leave
          example: C12345678
      required:
        - channel
    LeaveConversationResponseDTO:
      type: object
      properties: {}
    FormSelectionDTO:
      type: object
      properties:
        payload:
          type: object
          description: The Slack payload for form selection
          example:
            type: block_actions
            trigger_id: 123.456.abc
            team:
              id: T12345
              domain: example
            channel:
              id: C12345
              name: general
            actions:
              - action_id: form_continue
                block_id: form_selector_block
                value: form_123
            user:
              id: U12345
              name: user
      required:
        - payload
    FormSubmissionDTO:
      type: object
      properties:
        payload:
          type: object
          description: The Slack payload for form submission
          example:
            type: view_submission
            view:
              id: V12345
              callback_id: form_submission_modal
              private_metadata: '{"formId":"form_123","teamId":"T12345","channelId":"C12345"}'
              state:
                values:
                  field_block_1:
                    field_1:
                      value: Sample response
            team:
              id: T12345
              domain: example
            user:
              id: U12345
              name: user
      required:
        - payload
    FormInteractionDTO:
      type: object
      properties:
        payload:
          type: object
          description: The Slack payload for form interaction
          example:
            type: block_actions
            view:
              id: V12345
              callback_id: form_submission_modal
              private_metadata: '{"formId":"form_123","teamId":"T12345","channelId":"C12345"}'
              hash: hash123
              state:
                values: {}
            actions:
              - action_id: field_1
                block_id: field_block_1
                value: Selected value
            team:
              id: T12345
              domain: example
            user:
              id: U12345
              name: user
      required:
        - payload
    ErrorResponse:
      type: object
      properties:
        statusCode:
          type: integer
          description: HTTP status code
          example: 400
        message:
          oneOf:
            - type: string
              description: Error message
              example: Bad Request
            - type: array
              items:
                type: string
              description: List of error messages
              example:
                - Field is required
                - Invalid format
        error:
          type: string
          description: Error type
          example: Validation Error
    PaginationMeta:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 100
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 10
  parameters:
    TeamIdParam:
      name: teamId
      in: path
      required: true
      schema:
        type: string
      description: The ID of the Slack team/workspace
      example: T12345678
    ChannelIdParam:
      name: channelId
      in: path
      required: true
      schema:
        type: string
      description: The ID of the Slack channel
      example: C12345678
    UserIdParam:
      name: userId
      in: path
      required: true
      schema:
        type: string
      description: The ID of the Slack user
      example: U12345678
    PageParam:
      name: page
      in: query
      required: false
      schema:
        type: integer
        default: 1
        minimum: 1
      description: Page number for pagination
      example: 1
    LimitParam:
      name: limit
      in: query
      required: false
      schema:
        type: integer
        default: 10
        minimum: 1
        maximum: 100
      description: Number of items per page
      example: 10
security:
  - ApiKeyAuth: []
