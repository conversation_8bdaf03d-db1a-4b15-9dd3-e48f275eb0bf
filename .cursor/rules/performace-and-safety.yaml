# .cursor/rules/performance-and-safety.yaml

name: Performance and Safety Standards
description: Enforce high-performance code with thorough edge case handling for a large NestJS monorepo.

rules:
  - name: Optimize for Performance
    description: Ensure all code is optimized for high performance.
    applies_to: ["*.ts", "*.js"]
    conditions:
      - Avoid unnecessary computations in loops.
      - Use efficient data structures and algorithms.
      - Leverage database indexes for complex queries in TypeORM.
      - Minimize network calls and batch API requests when possible.

  - name: Thorough Edge Case Handling
    description: Consider and handle all potential edge cases.
    applies_to: ["*.ts", "*.js"]
    conditions:
      - Validate all inputs and handle invalid cases gracefully.
      - Implement error handling using NestJS's Exception Filters.
      - Ensure database transactions are atomic and handle rollbacks properly.

  - name: Monorepo Best Practices
    description: Maintain clean architecture within the Turborepo monorepo.
    applies_to: ["apps/**", "packages/**"]
    conditions:
      - Enforce module boundaries and prevent cross-dependency violations.
      - Use shared libraries for common utilities and types.
      - Keep build and test scripts optimized for parallel execution.

  - name: Type Safety and Code Quality
    description: Enforce strict type safety and maintain high code quality.
    applies_to: ["*.ts"]
    conditions:
      - Use strict TypeScript settings (`strict: true` in `tsconfig.json`).
      - Avoid `any` and prefer specific types.
      - Use DTOs for all API inputs/outputs in NestJS.

  - name: Database Best Practices
    description: Ensure efficient and safe interactions with PostgreSQL via TypeORM.
    applies_to: ["**/*.entity.ts", "**/*.repository.ts"]
    conditions:
      - Use parameterized queries to prevent SQL injection.
      - Optimize queries with proper indexing.
      - Use migrations for schema changes via Supabase.

  - name: Testing Standards
    description: Enforce high test coverage and performance-focused tests.
    applies_to: ["**/*.spec.ts", "**/*.test.ts"]
    conditions:
      - Write unit tests for all critical logic.
      - Include integration tests for complex flows.
      - Use mocks/stubs for external dependencies like AWS SQS and Typesense.
      - Ensure tests run efficiently in CI pipelines.
